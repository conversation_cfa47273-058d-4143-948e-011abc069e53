import { CashbackCategory } from '../types';

export const CASHBACK_CATEGORIES: CashbackCategory[] = [
  {
    id: 'food',
    name: 'Food & Dining',
    icon: '🍽️',
    color: '#ef4444',
    cashbackRate: 5,
    description: 'Restaurants, food delivery, groceries'
  },
  {
    id: 'travel',
    name: 'Travel',
    icon: '✈️',
    color: '#3b82f6',
    cashbackRate: 4,
    description: 'Flights, hotels, car rentals'
  },
  {
    id: 'ecommerce',
    name: 'E-Commerce',
    icon: '🛒',
    color: '#8b5cf6',
    cashbackRate: 3,
    description: 'Online shopping, digital purchases'
  },
  {
    id: 'fuel',
    name: 'Fuel & Gas',
    icon: '⛽',
    color: '#f59e0b',
    cashbackRate: 2,
    description: 'Gas stations, fuel purchases'
  },
  {
    id: 'entertainment',
    name: 'Entertainment',
    icon: '🎬',
    color: '#10b981',
    cashbackRate: 3,
    description: 'Movies, streaming, gaming'
  },
  {
    id: 'utilities',
    name: 'Utilities',
    icon: '💡',
    color: '#6b7280',
    cashbackRate: 1,
    description: 'Electricity, water, internet bills'
  }
];

export const SPENDING_THRESHOLDS = {
  LOW_USAGE: 5000, // ₹5,000
  SUGGESTION_THRESHOLD: 0.3, // 30%
  OPTIMIZATION_THRESHOLD: 0.25 // 25%
};

export const GAMIFICATION_CONFIG = {
  MAX_SCORE: 100,
  STREAK_BONUS: 1.1,
  PERFECT_MONTH_SCORE: 25,
  CATEGORY_SWITCH_SCORE: 15,
  AUTO_OPT_BONUS: 10
};

export const NOTIFICATION_TYPES = {
  SUGGESTION: 'suggestion',
  SUCCESS: 'success',
  WARNING: 'warning',
  INFO: 'info'
} as const;
