import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  Shield, 
  Info, 
  CheckCircle, 
  AlertTriangle,
  Settings
} from 'lucide-react';

interface AutoOptimizationToggleProps {
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
}

const AutoOptimizationToggle: React.FC<AutoOptimizationToggleProps> = ({
  enabled,
  onToggle
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleToggle = () => {
    if (!enabled) {
      // Show confirmation when enabling
      setShowConfirmation(true);
    } else {
      // Disable immediately
      onToggle(false);
    }
  };

  const handleConfirmEnable = () => {
    onToggle(true);
    setShowConfirmation(false);
  };

  return (
    <div className="space-y-4">
      {/* Main Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <motion.div
            animate={{ 
              scale: enabled ? [1, 1.1, 1] : 1,
              rotate: enabled ? [0, 5, -5, 0] : 0
            }}
            transition={{ 
              duration: 2, 
              repeat: enabled ? Infinity : 0,
              repeatDelay: 3
            }}
          >
            <Zap className={`w-6 h-6 ${enabled ? 'text-yellow-500' : 'text-gray-400'}`} />
          </motion.div>
          
          <div>
            <h4 className="font-semibold text-gray-900">Auto-Optimization</h4>
            <p className="text-sm text-gray-600">
              Automatically switch to the best category monthly
            </p>
          </div>
        </div>
        
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={handleToggle}
          className={`relative w-14 h-8 rounded-full transition-colors duration-200 ${
            enabled ? 'bg-green-500' : 'bg-gray-300'
          }`}
        >
          <motion.div
            className="absolute top-1 w-6 h-6 bg-white rounded-full shadow-md"
            animate={{ x: enabled ? 24 : 4 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          />
        </motion.button>
      </div>

      {/* Status Indicator */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex items-center gap-2 p-3 rounded-lg ${
          enabled 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-gray-50 border border-gray-200'
        }`}
      >
        {enabled ? (
          <>
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-sm text-green-800 font-medium">
              Auto-optimization is active
            </span>
          </>
        ) : (
          <>
            <Settings className="w-5 h-5 text-gray-600" />
            <span className="text-sm text-gray-700">
              Manual category selection
            </span>
          </>
        )}
      </motion.div>

      {/* Details Toggle */}
      <button
        onClick={() => setShowDetails(!showDetails)}
        className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors"
      >
        <Info className="w-4 h-4" />
        <span>How does auto-optimization work?</span>
      </button>

      {/* Expandable Details */}
      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <h5 className="font-medium text-blue-900 mb-3">Auto-Optimization Features:</h5>
            
            <div className="space-y-3 text-sm text-blue-800">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Monthly Analysis:</strong> Reviews your spending patterns every billing cycle
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Smart Switching:</strong> Automatically selects the category with highest cashback potential
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Notifications:</strong> Sends confirmation alerts when changes are made
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Safety Checks:</strong> Only switches when improvement is significant (>25%)
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <Shield className="w-4 h-4" />
                <span className="font-medium text-sm">Privacy & Control</span>
              </div>
              <p className="text-xs text-yellow-700 mt-1">
                You can disable auto-optimization anytime. All changes are logged and you receive notifications before any switches.
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Confirmation Modal */}
      <AnimatePresence>
        {showConfirmation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowConfirmation(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Enable Auto-Optimization?
                </h3>
                <p className="text-gray-600 mb-6">
                  This will allow the system to automatically switch your cashback category 
                  based on your spending patterns to maximize rewards.
                </p>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center gap-2 text-blue-800 mb-2">
                    <Info className="w-5 h-5" />
                    <span className="font-medium">What happens next?</span>
                  </div>
                  <ul className="text-sm text-blue-700 space-y-1 text-left">
                    <li>• Monthly analysis of your spending patterns</li>
                    <li>• Automatic category switches when beneficial</li>
                    <li>• Notification alerts for all changes</li>
                    <li>• You can disable this feature anytime</li>
                  </ul>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <AlertTriangle className="w-5 h-5" />
                    <span className="font-medium text-sm">Consent Required</span>
                  </div>
                  <p className="text-xs text-yellow-700 mt-1">
                    By enabling this feature, you consent to automated category changes based on spending analysis.
                  </p>
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowConfirmation(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirmEnable}
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <Zap className="w-4 h-4" />
                    Enable Auto-Optimization
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AutoOptimizationToggle;
