import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowR<PERSON>, 
  Check, 
  Clock, 
  AlertCircle, 
  Sparkles,
  TrendingUp,
  Target
} from 'lucide-react';
import { CashbackCategory, CategorySuggestion } from '../types';
import { CASHBACK_CATEGORIES } from '../constants/categories';

interface CategorySwitcherProps {
  currentCategory: CashbackCategory;
  suggestions: CategorySuggestion[];
  onCategorySwitch: (category: CashbackCategory) => void;
  isLoading: boolean;
}

const CategorySwitcher: React.FC<CategorySwitcherProps> = ({
  currentCategory,
  suggestions,
  onCategorySwitch,
  isLoading
}) => {
  const [selectedCategory, setSelectedCategory] = useState<CashbackCategory | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleCategorySelect = (category: CashbackCategory) => {
    if (category.id === currentCategory.id) return;
    
    setSelectedCategory(category);
    setShowConfirmation(true);
  };

  const handleConfirmSwitch = () => {
    if (selectedCategory) {
      onCategorySwitch(selectedCategory);
      setShowConfirmation(false);
      setSelectedCategory(null);
    }
  };

  const getSuggestionForCategory = (categoryId: string) => {
    return suggestions.find(s => s.suggestedCategory.id === categoryId);
  };

  const getRecommendationBadge = (category: CashbackCategory) => {
    const suggestion = getSuggestionForCategory(category.id);
    if (!suggestion) return null;

    const impact = suggestion.monthlyImpact;
    if (impact > 1000) return { text: 'High Impact', color: 'bg-red-100 text-red-800' };
    if (impact > 500) return { text: 'Medium Impact', color: 'bg-yellow-100 text-yellow-800' };
    return { text: 'Low Impact', color: 'bg-blue-100 text-blue-800' };
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-lg p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Target className="w-6 h-6 text-purple-600" />
            Category Switcher
          </h3>
          <p className="text-gray-600 text-sm mt-1">
            One-tap switch • Changes once per billing cycle
          </p>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Clock className="w-4 h-4" />
          <span>Last changed: 45 days ago</span>
        </div>
      </div>

      {/* Current Category */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="text-3xl">{currentCategory.icon}</div>
            <div>
              <div className="font-semibold text-gray-900">{currentCategory.name}</div>
              <div className="text-sm text-gray-600">{currentCategory.description}</div>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm font-medium text-blue-600">
                  {currentCategory.cashbackRate}% cashback
                </span>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  Current
                </span>
              </div>
            </div>
          </div>
          
          <Check className="w-6 h-6 text-green-600" />
        </div>
      </div>

      {/* Category Options */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900 flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-yellow-500" />
          Available Categories
        </h4>
        
        {CASHBACK_CATEGORIES.map((category) => {
          const suggestion = getSuggestionForCategory(category.id);
          const badge = getRecommendationBadge(category);
          const isCurrent = category.id === currentCategory.id;
          
          return (
            <motion.div
              key={category.id}
              whileHover={!isCurrent ? { scale: 1.02 } : {}}
              whileTap={!isCurrent ? { scale: 0.98 } : {}}
              className={`relative overflow-hidden rounded-xl border-2 transition-all cursor-pointer ${
                isCurrent
                  ? 'border-blue-300 bg-blue-50 cursor-default'
                  : suggestion
                  ? 'border-orange-200 bg-orange-50 hover:border-orange-300'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
              onClick={() => !isCurrent && handleCategorySelect(category)}
            >
              {/* Suggestion Glow Effect */}
              {suggestion && (
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-pink-400 opacity-10" />
              )}
              
              <div className="relative p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{category.icon}</div>
                    <div>
                      <div className="font-medium text-gray-900 flex items-center gap-2">
                        {category.name}
                        {badge && (
                          <span className={`px-2 py-1 text-xs rounded-full ${badge.color}`}>
                            {badge.text}
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">{category.description}</div>
                      <div className="flex items-center gap-3 mt-1">
                        <span className="text-sm font-medium" style={{ color: category.color }}>
                          {category.cashbackRate}% cashback
                        </span>
                        {suggestion && (
                          <span className="text-sm text-green-600 font-medium">
                            +₹{Math.round(suggestion.monthlyImpact)}/month
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {suggestion && (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="flex items-center gap-1 text-orange-600"
                      >
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-sm font-medium">Recommended</span>
                      </motion.div>
                    )}
                    
                    {isCurrent ? (
                      <Check className="w-5 h-5 text-green-600" />
                    ) : (
                      <ArrowRight className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </div>
                
                {suggestion && (
                  <div className="mt-3 pt-3 border-t border-orange-200">
                    <div className="text-sm text-gray-700">
                      <strong>Why this category?</strong> {suggestion.reason}
                    </div>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-600">
                      <span>Confidence: {suggestion.confidence}%</span>
                      <span>Potential increase: ₹{Math.round(suggestion.potentialIncrease)}</span>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Confirmation Modal */}
      <AnimatePresence>
        {showConfirmation && selectedCategory && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowConfirmation(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className="text-4xl mb-4">{selectedCategory.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Switch to {selectedCategory.name}?
                </h3>
                <p className="text-gray-600 mb-6">
                  This will change your cashback category for the current billing cycle. 
                  You can only change once per cycle.
                </p>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <AlertCircle className="w-5 h-5" />
                    <span className="font-medium">Important</span>
                  </div>
                  <p className="text-sm text-yellow-700 mt-1">
                    This change will take effect immediately and cannot be undone until next billing cycle.
                  </p>
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowConfirmation(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirmSwitch}
                    disabled={isLoading}
                    className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {isLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                      />
                    ) : (
                      <>
                        <Check className="w-4 h-4" />
                        Confirm Switch
                      </>
                    )}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CategorySwitcher;
