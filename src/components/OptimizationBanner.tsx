import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  X, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Target
} from 'lucide-react';
import { CategorySuggestion } from '../types';

interface OptimizationBannerProps {
  suggestion: CategorySuggestion;
  onSwitch: () => void;
  isLoading: boolean;
}

const OptimizationBanner: React.FC<OptimizationBannerProps> = ({
  suggestion,
  onSwitch,
  isLoading
}) => {
  const [isDismissed, setIsDismissed] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  if (isDismissed) return null;

  const monthlyIncrease = Math.round(suggestion.monthlyImpact);
  const yearlyIncrease = Math.round(suggestion.monthlyImpact * 12);
  const confidenceColor = suggestion.confidence >= 80 ? 'text-green-600' : 
                         suggestion.confidence >= 60 ? 'text-yellow-600' : 'text-red-600';

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -50, scale: 0.95 }}
        className="relative overflow-hidden"
      >
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 opacity-90" />
        <div className="absolute inset-0 bg-black opacity-10" />
        
        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full opacity-30"
              animate={{
                x: [0, 100, 0],
                y: [0, -50, 0],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 3 + i,
                repeat: Infinity,
                delay: i * 0.5,
              }}
              style={{
                left: `${10 + i * 15}%`,
                top: `${20 + i * 10}%`,
              }}
            />
          ))}
        </div>

        <div className="relative bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 border border-white border-opacity-20">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-3xl"
              >
                {suggestion.suggestedCategory.icon}
              </motion.div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-yellow-500" />
                  You're missing out on cashback!
                </h3>
                <p className="text-gray-600 text-sm">
                  Smart optimization suggestion based on your spending
                </p>
              </div>
            </div>
            
            <button
              onClick={() => setIsDismissed(true)}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Side - Suggestion */}
            <div>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Switch from</span>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Switch to</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="text-center">
                    <div className="text-2xl mb-1">{suggestion.currentCategory.icon}</div>
                    <div className="text-sm font-medium text-gray-700">
                      {suggestion.currentCategory.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {suggestion.currentCategory.cashbackRate}% cashback
                    </div>
                  </div>
                  
                  <motion.div
                    animate={{ x: [0, 10, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-6 h-6 text-purple-600" />
                  </motion.div>
                  
                  <div className="text-center">
                    <div className="text-2xl mb-1">{suggestion.suggestedCategory.icon}</div>
                    <div className="text-sm font-medium text-gray-700">
                      {suggestion.suggestedCategory.name}
                    </div>
                    <div className="text-xs text-green-600 font-medium">
                      {suggestion.suggestedCategory.cashbackRate}% cashback
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-600 mb-4">
                {suggestion.reason}
              </div>

              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Target className="w-4 h-4 text-purple-600" />
                  <span className={`font-medium ${confidenceColor}`}>
                    {suggestion.confidence}% confidence
                  </span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4 text-blue-600" />
                  <span className="text-gray-600">
                    Based on 3-month analysis
                  </span>
                </div>
              </div>
            </div>

            {/* Right Side - Impact */}
            <div>
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 mb-4">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  Potential Impact
                </h4>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Monthly increase:</span>
                    <span className="font-bold text-green-600 text-lg">
                      +₹{monthlyIncrease.toLocaleString()}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Yearly potential:</span>
                    <span className="font-bold text-green-600 text-lg">
                      +₹{yearlyIncrease.toLocaleString()}
                    </span>
                  </div>
                  
                  <div className="pt-2 border-t border-green-200">
                    <div className="text-xs text-gray-500 mb-1">
                      Total potential increase
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      ₹{Math.round(suggestion.potentialIncrease).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onSwitch}
                  disabled={isLoading}
                  className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                    />
                  ) : (
                    <>
                      <Sparkles className="w-5 h-5" />
                      Switch Now
                    </>
                  )}
                </motion.button>
                
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="px-4 py-3 border border-gray-300 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-colors"
                >
                  Details
                </button>
              </div>
            </div>
          </div>

          {/* Expandable Details */}
          <AnimatePresence>
            {showDetails && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-6 pt-6 border-t border-gray-200"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="font-medium text-blue-900 mb-1">Analysis Period</div>
                    <div className="text-blue-700">Last 3 months of spending</div>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-3">
                    <div className="font-medium text-purple-900 mb-1">Calculation Method</div>
                    <div className="text-purple-700">Category-wise cashback optimization</div>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="font-medium text-green-900 mb-1">Next Review</div>
                    <div className="text-green-700">End of current billing cycle</div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default OptimizationBanner;
