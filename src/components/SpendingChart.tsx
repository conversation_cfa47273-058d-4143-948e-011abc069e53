import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>lt<PERSON>, Legend, BarChart, Bar, XAxis, YAxis } from 'recharts';
import { <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON> as Pie<PERSON><PERSON>I<PERSON>, Info, TrendingUp } from 'lucide-react';
import { SpendingAnalysis, CashbackCategory } from '../types';

interface SpendingChartProps {
  data: SpendingAnalysis[];
  currentCategory: CashbackCategory;
}

const SpendingChart: React.FC<SpendingChartProps> = ({ data, currentCategory }) => {
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  const chartData = data.map(item => ({
    name: item.category.name,
    value: item.totalSpent,
    percentage: item.percentage,
    color: item.category.color,
    icon: item.category.icon,
    cashbackRate: item.category.cashbackRate,
    isSelected: item.category.id === currentCategory.id,
    missedCashback: item.missedCashback,
    actualCashback: item.actualCashback,
    potentialCashback: item.potentialCashback
  }));

  const totalSpent = data.reduce((sum, item) => sum + item.totalSpent, 0);
  const totalMissedCashback = data.reduce((sum, item) => sum + item.missedCashback, 0);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 rounded-lg shadow-lg border border-gray-200 min-w-[200px]">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">{data.icon}</span>
            <span className="font-semibold text-gray-900">{data.name}</span>
          </div>
          
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Amount Spent:</span>
              <span className="font-medium">₹{data.value.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Percentage:</span>
              <span className="font-medium">{data.percentage.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cashback Rate:</span>
              <span className="font-medium text-blue-600">{data.cashbackRate}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Actual Cashback:</span>
              <span className="font-medium text-green-600">₹{Math.round(data.actualCashback)}</span>
            </div>
            {data.missedCashback > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">Missed Cashback:</span>
                <span className="font-medium text-red-600">₹{Math.round(data.missedCashback)}</span>
              </div>
            )}
          </div>
          
          {data.isSelected && (
            <div className="mt-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full text-center">
              Current Category
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <motion.div
            key={index}
            whileHover={{ scale: 1.05 }}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-all ${
              entry.payload.isSelected 
                ? 'bg-blue-100 border-2 border-blue-300' 
                : 'bg-gray-50 hover:bg-gray-100'
            }`}
            onMouseEnter={() => setShowTooltip(entry.value)}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <span className="text-lg">{entry.payload.icon}</span>
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
            <span className="text-sm font-medium text-gray-700">{entry.value}</span>
            <span className="text-xs text-gray-500">
              {entry.payload.percentage.toFixed(1)}%
            </span>
          </motion.div>
        ))}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-lg p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <TrendingUp className="w-6 h-6 text-blue-600" />
            Spending Analysis
          </h3>
          <p className="text-gray-600 text-sm mt-1">
            Last 3 months • Total: ₹{totalSpent.toLocaleString()}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setChartType('pie')}
            className={`p-2 rounded-lg transition-colors ${
              chartType === 'pie' 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <PieChartIcon className="w-5 h-5" />
          </button>
          <button
            onClick={() => setChartType('bar')}
            className={`p-2 rounded-lg transition-colors ${
              chartType === 'bar' 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <BarChart3 className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'pie' ? (
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                outerRadius={100}
                innerRadius={40}
                paddingAngle={2}
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color}
                    stroke={entry.isSelected ? '#3b82f6' : 'transparent'}
                    strokeWidth={entry.isSelected ? 3 : 0}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={<CustomLegend />} />
            </PieChart>
          ) : (
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="value" 
                radius={[4, 4, 0, 0]}
                fill={(entry: any) => entry.color}
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color}
                    stroke={entry.isSelected ? '#3b82f6' : 'transparent'}
                    strokeWidth={entry.isSelected ? 2 : 0}
                  />
                ))}
              </Bar>
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-3 h-3 bg-green-500 rounded-full" />
            <span className="text-sm font-medium text-green-800">Current Category</span>
          </div>
          <div className="text-lg font-bold text-green-900">
            {currentCategory.icon} {currentCategory.name}
          </div>
          <div className="text-sm text-green-700">
            {currentCategory.cashbackRate}% cashback rate
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Top Category</span>
          </div>
          <div className="text-lg font-bold text-blue-900">
            {chartData[0]?.icon} {chartData[0]?.name}
          </div>
          <div className="text-sm text-blue-700">
            {chartData[0]?.percentage.toFixed(1)}% of total spending
          </div>
        </div>

        {totalMissedCashback > 0 && (
          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Info className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">Missed Opportunity</span>
            </div>
            <div className="text-lg font-bold text-orange-900">
              ₹{Math.round(totalMissedCashback)}
            </div>
            <div className="text-sm text-orange-700">
              Potential additional cashback
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default SpendingChart;
