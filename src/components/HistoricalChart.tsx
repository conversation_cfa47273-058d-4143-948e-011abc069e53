import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend
} from 'recharts';
import { Calendar, TrendingUp, BarChart3, Activity } from 'lucide-react';
import { SpendingAnalysis, TimeRange } from '../types';
import { format, subMonths, eachMonthOfInterval } from 'date-fns';

interface HistoricalChartProps {
  spendingAnalysis: SpendingAnalysis[];
  timeRange: TimeRange;
}

const HistoricalChart: React.FC<HistoricalChartProps> = ({
  spendingAnalysis,
  timeRange
}) => {
  const [chartType, setChartType] = useState<'area' | 'bar'>('area');
  const [selectedMetric, setSelectedMetric] = useState<'spending' | 'cashback'>('spending');

  // Generate historical data (mock data for demonstration)
  const historicalData = useMemo(() => {
    const months = timeRange === '1M' ? 1 : timeRange === '3M' ? 3 : timeRange === '6M' ? 6 : 12;
    const endDate = new Date();
    const startDate = subMonths(endDate, months - 1);
    
    const monthsArray = eachMonthOfInterval({ start: startDate, end: endDate });
    
    return monthsArray.map((month, index) => {
      const monthData: any = {
        month: format(month, 'MMM yyyy'),
        monthShort: format(month, 'MMM'),
        total: 0
      };
      
      // Generate realistic spending patterns for each category
      spendingAnalysis.forEach(analysis => {
        const baseAmount = analysis.totalSpent / 3; // Average monthly
        const variation = (Math.random() - 0.5) * 0.4; // ±20% variation
        const seasonalFactor = analysis.category.id === 'travel' && (index % 4 === 0) ? 1.5 : 1;
        
        const monthlySpending = Math.max(0, baseAmount * (1 + variation) * seasonalFactor);
        const monthlyCashback = (monthlySpending * analysis.category.cashbackRate) / 100;
        
        monthData[analysis.category.name] = Math.round(monthlySpending);
        monthData[`${analysis.category.name}_cashback`] = Math.round(monthlyCashback);
        monthData.total += monthlySpending;
      });
      
      return monthData;
    });
  }, [spendingAnalysis, timeRange]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 rounded-lg shadow-lg border border-gray-200">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          <div className="space-y-1">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-sm text-gray-700">{entry.dataKey}</span>
                </div>
                <span className="font-medium">
                  ₹{entry.value.toLocaleString()}
                </span>
              </div>
            ))}
          </div>
          <div className="mt-2 pt-2 border-t border-gray-200">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Total:</span>
              <span className="font-semibold">
                ₹{payload.reduce((sum: number, entry: any) => sum + entry.value, 0).toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const timeRangeOptions: { value: TimeRange; label: string }[] = [
    { value: '1M', label: '1 Month' },
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' }
  ];

  const getDataKey = (category: SpendingAnalysis) => {
    return selectedMetric === 'spending' 
      ? category.category.name 
      : `${category.category.name}_cashback`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-lg p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Activity className="w-6 h-6 text-green-600" />
            Historical Trends
          </h3>
          <p className="text-gray-600 text-sm mt-1">
            Category-wise {selectedMetric} over time
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Metric Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setSelectedMetric('spending')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                selectedMetric === 'spending'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Spending
            </button>
            <button
              onClick={() => setSelectedMetric('cashback')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                selectedMetric === 'cashback'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Cashback
            </button>
          </div>
          
          {/* Chart Type Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setChartType('area')}
              className={`p-2 rounded-md transition-colors ${
                chartType === 'area'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <TrendingUp className="w-4 h-4" />
            </button>
            <button
              onClick={() => setChartType('bar')}
              className={`p-2 rounded-md transition-colors ${
                chartType === 'bar'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <BarChart3 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'area' ? (
            <AreaChart data={historicalData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="monthShort" 
                tick={{ fontSize: 12 }}
                stroke="#6b7280"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}k`}
                stroke="#6b7280"
              />
              <Tooltip content={<CustomTooltip />} />
              
              {spendingAnalysis.map((analysis, index) => (
                <Area
                  key={analysis.category.id}
                  type="monotone"
                  dataKey={getDataKey(analysis)}
                  stackId="1"
                  stroke={analysis.category.color}
                  fill={analysis.category.color}
                  fillOpacity={0.6}
                />
              ))}
            </AreaChart>
          ) : (
            <BarChart data={historicalData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="monthShort" 
                tick={{ fontSize: 12 }}
                stroke="#6b7280"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}k`}
                stroke="#6b7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {spendingAnalysis.slice(0, 4).map((analysis, index) => (
                <Bar
                  key={analysis.category.id}
                  dataKey={getDataKey(analysis)}
                  fill={analysis.category.color}
                  radius={[2, 2, 0, 0]}
                />
              ))}
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Trend</span>
          </div>
          <div className="text-lg font-bold text-blue-900">
            {historicalData.length > 1 && 
             historicalData[historicalData.length - 1].total > historicalData[0].total 
             ? '↗️ Increasing' : '↘️ Decreasing'}
          </div>
          <div className="text-sm text-blue-700">
            Overall spending pattern
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="w-5 h-5 text-green-600" />
            <span className="text-sm font-medium text-green-800">Average Monthly</span>
          </div>
          <div className="text-lg font-bold text-green-900">
            ₹{Math.round(historicalData.reduce((sum, month) => sum + month.total, 0) / historicalData.length).toLocaleString()}
          </div>
          <div className="text-sm text-green-700">
            {selectedMetric === 'spending' ? 'Spending' : 'Cashback earned'}
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <BarChart3 className="w-5 h-5 text-purple-600" />
            <span className="text-sm font-medium text-purple-800">Peak Month</span>
          </div>
          <div className="text-lg font-bold text-purple-900">
            {historicalData.reduce((max, month) => month.total > max.total ? month : max, historicalData[0])?.monthShort || 'N/A'}
          </div>
          <div className="text-sm text-purple-700">
            Highest {selectedMetric} month
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default HistoricalChart;
