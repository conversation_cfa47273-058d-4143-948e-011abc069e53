import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Award, 
  Trophy, 
  Target, 
  Flame, 
  Star, 
  Gift,
  ChevronRight,
  Lock,
  Unlock
} from 'lucide-react';
import { GamificationData } from '../types';

interface GamificationPanelProps {
  gamificationData: GamificationData;
  optimizationScore: number;
}

const GamificationPanel: React.FC<GamificationPanelProps> = ({
  gamificationData,
  optimizationScore
}) => {
  const [showAchievements, setShowAchievements] = useState(false);

  const scorePercentage = (optimizationScore / gamificationData.maxScore) * 100;
  const nextMilestoneProgress = (optimizationScore / gamificationData.nextMilestone.targetScore) * 100;

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreGradient = (score: number) => {
    if (score >= 85) return 'from-green-500 to-emerald-600';
    if (score >= 70) return 'from-yellow-500 to-orange-600';
    return 'from-red-500 to-pink-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-lg p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Trophy className="w-6 h-6 text-yellow-600" />
            Optimization Score
          </h3>
          <p className="text-gray-600 text-sm mt-1">
            Track your cashback optimization performance
          </p>
        </div>
        
        <button
          onClick={() => setShowAchievements(!showAchievements)}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <Award className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Score Display */}
      <div className="relative mb-6">
        <div className="flex items-center justify-center">
          <div className="relative">
            {/* Circular Progress */}
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
              {/* Background Circle */}
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="#e5e7eb"
                strokeWidth="8"
              />
              {/* Progress Circle */}
              <motion.circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="url(#scoreGradient)"
                strokeWidth="8"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 50}`}
                strokeDashoffset={`${2 * Math.PI * 50 * (1 - scorePercentage / 100)}`}
                initial={{ strokeDashoffset: 2 * Math.PI * 50 }}
                animate={{ strokeDashoffset: 2 * Math.PI * 50 * (1 - scorePercentage / 100) }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
              
              {/* Gradient Definition */}
              <defs>
                <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor={optimizationScore >= 85 ? '#10b981' : optimizationScore >= 70 ? '#f59e0b' : '#ef4444'} />
                  <stop offset="100%" stopColor={optimizationScore >= 85 ? '#059669' : optimizationScore >= 70 ? '#d97706' : '#dc2626'} />
                </linearGradient>
              </defs>
            </svg>
            
            {/* Score Text */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, type: "spring" }}
                  className={`text-3xl font-bold ${getScoreColor(optimizationScore)}`}
                >
                  {Math.round(optimizationScore)}
                </motion.div>
                <div className="text-sm text-gray-600">out of 100</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Score Description */}
        <div className="text-center mt-4">
          <div className={`text-lg font-semibold ${getScoreColor(optimizationScore)}`}>
            {optimizationScore >= 85 ? 'Excellent!' : 
             optimizationScore >= 70 ? 'Good' : 'Needs Improvement'}
          </div>
          <div className="text-sm text-gray-600">
            {optimizationScore >= 85 ? 'You\'re maximizing your cashback potential' : 
             optimizationScore >= 70 ? 'Room for optimization improvements' : 
             'Consider switching to a better category'}
          </div>
        </div>
      </div>

      {/* Streak & Bonus */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Flame className="w-5 h-5 text-orange-600" />
            <span className="text-sm font-medium text-orange-800">Streak</span>
          </div>
          <div className="text-2xl font-bold text-orange-900">
            {gamificationData.streak}
          </div>
          <div className="text-sm text-orange-700">
            months optimized
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Star className="w-5 h-5 text-purple-600" />
            <span className="text-sm font-medium text-purple-800">Bonus</span>
          </div>
          <div className="text-2xl font-bold text-purple-900">
            {((gamificationData.bonusMultiplier - 1) * 100).toFixed(0)}%
          </div>
          <div className="text-sm text-purple-700">
            extra cashback
          </div>
        </div>
      </div>

      {/* Next Milestone */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Target className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-900">Next Milestone</span>
          </div>
          <span className="text-sm text-blue-700">
            {Math.round(nextMilestoneProgress)}%
          </span>
        </div>
        
        <div className="mb-3">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-blue-800 font-medium">
              {gamificationData.nextMilestone.title}
            </span>
            <span className="text-blue-600">
              {optimizationScore}/{gamificationData.nextMilestone.targetScore}
            </span>
          </div>
          
          <div className="w-full bg-blue-200 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(100, nextMilestoneProgress)}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Gift className="w-4 h-4 text-blue-600" />
          <span className="text-sm text-blue-700">
            Reward: {gamificationData.nextMilestone.reward}
          </span>
        </div>
      </div>

      {/* Achievements Toggle */}
      <button
        onClick={() => setShowAchievements(!showAchievements)}
        className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <div className="flex items-center gap-2">
          <Award className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Achievements</span>
          <span className="text-sm text-gray-600">
            ({gamificationData.achievements.filter(a => a.unlockedAt).length}/{gamificationData.achievements.length})
          </span>
        </div>
        <ChevronRight 
          className={`w-5 h-5 text-gray-600 transition-transform ${
            showAchievements ? 'rotate-90' : ''
          }`} 
        />
      </button>

      {/* Achievements List */}
      <AnimatePresence>
        {showAchievements && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 space-y-3"
          >
            {gamificationData.achievements.map((achievement) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`flex items-center gap-3 p-3 rounded-lg border ${
                  achievement.unlockedAt 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className="text-2xl">
                  {achievement.unlockedAt ? achievement.icon : '🔒'}
                </div>
                
                <div className="flex-1">
                  <div className={`font-medium ${
                    achievement.unlockedAt ? 'text-green-900' : 'text-gray-600'
                  }`}>
                    {achievement.title}
                  </div>
                  <div className={`text-sm ${
                    achievement.unlockedAt ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    {achievement.description}
                  </div>
                  
                  {!achievement.unlockedAt && (
                    <div className="mt-2">
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{achievement.progress}/{achievement.maxProgress}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div 
                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                <div>
                  {achievement.unlockedAt ? (
                    <Unlock className="w-5 h-5 text-green-600" />
                  ) : (
                    <Lock className="w-5 h-5 text-gray-400" />
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default GamificationPanel;
