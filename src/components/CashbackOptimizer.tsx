import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  Target, 
  Settings, 
  Bell, 
  Award,
  ArrowRight,
  RefreshCw,
  Info
} from 'lucide-react';

import { DashboardData, CashbackCategory, CategorySuggestion, SpendingAnalysis } from '../types';
import { SpendAnalysisEngine } from '../utils/spendAnalysis';
import { SuggestionEngine } from '../utils/suggestionEngine';
import { generateMockDashboardData, generateMockTransactions } from '../data/mockData';

import OptimizationBanner from './OptimizationBanner';
import Spending<PERSON>hart from './SpendingChart';
import CategorySwitcher from './CategorySwitcher';
import GamificationPanel from './GamificationPanel';
import NotificationCenter from './NotificationCenter';
import HistoricalChart from './HistoricalChart';
import AutoOptimizationToggle from './AutoOptimizationToggle';

const CashbackOptimizer: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>(generateMockDashboardData());
  const [isLoading, setIsLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Generate transactions and analysis
  const transactions = useMemo(() => generateMockTransactions(), []);
  
  const spendingAnalysis = useMemo(() => {
    return SpendAnalysisEngine.analyzeSpending(
      transactions,
      dashboardData.user.currentCategory,
      3
    );
  }, [transactions, dashboardData.user.currentCategory]);

  const suggestions = useMemo(() => {
    const totalMonthlySpend = spendingAnalysis.reduce((sum, s) => sum + s.totalSpent, 0) / 3;
    return SuggestionEngine.generatePersonalizedSuggestions(
      spendingAnalysis,
      dashboardData.user.currentCategory,
      totalMonthlySpend,
      {}
    );
  }, [spendingAnalysis, dashboardData.user.currentCategory]);

  const optimizationScore = useMemo(() => {
    return SpendAnalysisEngine.calculateOptimizationScore(
      spendingAnalysis,
      dashboardData.user.currentCategory
    );
  }, [spendingAnalysis, dashboardData.user.currentCategory]);

  useEffect(() => {
    setDashboardData(prev => ({
      ...prev,
      spendingAnalysis,
      suggestions,
      user: {
        ...prev.user,
        optimizationScore
      }
    }));
  }, [spendingAnalysis, suggestions, optimizationScore]);

  const handleCategorySwitch = async (newCategory: CashbackCategory) => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setDashboardData(prev => ({
      ...prev,
      user: {
        ...prev.user,
        currentCategory: newCategory,
        lastCategoryChange: new Date()
      }
    }));
    
    setIsLoading(false);
  };

  const handleAutoOptimizationToggle = (enabled: boolean) => {
    setDashboardData(prev => ({
      ...prev,
      user: {
        ...prev.user,
        autoOptimizationEnabled: enabled
      }
    }));
  };

  const totalSpent = spendingAnalysis.reduce((sum, s) => sum + s.totalSpent, 0);
  const totalCashback = spendingAnalysis.reduce((sum, s) => sum + s.actualCashback, 0);
  const potentialCashback = suggestions.length > 0 ? suggestions[0].potentialIncrease : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Target className="text-blue-600" />
                Smart Cashback Optimizer
              </h1>
              <p className="text-gray-600 mt-2">
                Maximize your rewards with intelligent category suggestions
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <NotificationCenter notifications={dashboardData.notifications} />
              
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-3 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                <Settings className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Spent (3M)</p>
                <p className="text-2xl font-bold text-gray-900">
                  ₹{totalSpent.toLocaleString()}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-600" />
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cashback Earned</p>
                <p className="text-2xl font-bold text-green-600">
                  ₹{Math.round(totalCashback).toLocaleString()}
                </p>
              </div>
              <Award className="w-8 h-8 text-green-600" />
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Optimization Score</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(optimizationScore)}%
                </p>
              </div>
              <Target className="w-8 h-8 text-purple-600" />
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Potential Increase</p>
                <p className="text-2xl font-bold text-orange-600">
                  ₹{Math.round(potentialCashback).toLocaleString()}
                </p>
              </div>
              <ArrowRight className="w-8 h-8 text-orange-600" />
            </div>
          </motion.div>
        </div>

        {/* Optimization Banner */}
        {suggestions.length > 0 && (
          <OptimizationBanner 
            suggestion={suggestions[0]}
            onSwitch={() => handleCategorySwitch(suggestions[0].suggestedCategory)}
            isLoading={isLoading}
          />
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            <SpendingChart 
              data={spendingAnalysis}
              currentCategory={dashboardData.user.currentCategory}
            />
            
            <HistoricalChart 
              spendingAnalysis={spendingAnalysis}
              timeRange="3M"
            />
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <CategorySwitcher
              currentCategory={dashboardData.user.currentCategory}
              suggestions={suggestions}
              onCategorySwitch={handleCategorySwitch}
              isLoading={isLoading}
            />
            
            <GamificationPanel 
              gamificationData={dashboardData.gamification}
              optimizationScore={optimizationScore}
            />
            
            {showSettings && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white rounded-xl shadow-lg p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Settings
                </h3>
                
                <AutoOptimizationToggle
                  enabled={dashboardData.user.autoOptimizationEnabled}
                  onToggle={handleAutoOptimizationToggle}
                />
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CashbackOptimizer;
