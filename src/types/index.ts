export interface Transaction {
  id: string;
  amount: number;
  category: CashbackCategory;
  merchant: string;
  date: Date;
  description: string;
  cashbackEarned: number;
}

export interface CashbackCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  cashbackRate: number;
  description: string;
}

export interface SpendingAnalysis {
  category: CashbackCategory;
  totalSpent: number;
  transactionCount: number;
  percentage: number;
  potentialCashback: number;
  actualCashback: number;
  missedCashback: number;
}

export interface CategorySuggestion {
  suggestedCategory: CashbackCategory;
  currentCategory: CashbackCategory;
  potentialIncrease: number;
  confidence: number;
  reason: string;
  monthlyImpact: number;
}

export interface UserPreferences {
  userId: string;
  currentCategory: CashbackCategory;
  autoOptimizationEnabled: boolean;
  notificationsEnabled: boolean;
  lastCategoryChange: Date | null;
  optimizationScore: number;
  consecutiveOptimizedMonths: number;
}

export interface BillingCycle {
  id: string;
  startDate: Date;
  endDate: Date;
  totalSpent: number;
  totalCashback: number;
  optimizationScore: number;
}

export interface NotificationData {
  id: string;
  type: 'suggestion' | 'success' | 'warning' | 'info';
  title: string;
  message: string;
  actionText?: string;
  actionCallback?: () => void;
  timestamp: Date;
  isRead: boolean;
}

export interface GamificationData {
  currentScore: number;
  maxScore: number;
  streak: number;
  bonusMultiplier: number;
  achievements: Achievement[];
  nextMilestone: Milestone;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date | null;
  progress: number;
  maxProgress: number;
}

export interface Milestone {
  title: string;
  description: string;
  targetScore: number;
  reward: string;
}

export interface DashboardData {
  user: UserPreferences;
  currentBillingCycle: BillingCycle;
  spendingAnalysis: SpendingAnalysis[];
  suggestions: CategorySuggestion[];
  recentTransactions: Transaction[];
  gamification: GamificationData;
  notifications: NotificationData[];
}

export type TimeRange = '1M' | '3M' | '6M' | '1Y';

export interface ChartDataPoint {
  name: string;
  value: number;
  color: string;
  percentage: number;
}
