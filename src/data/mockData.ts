import { Transaction, UserPreferences, BillingCycle, GamificationData, Achievement, DashboardData } from '../types';
import { CASHBACK_CATEGORIES } from '../constants/categories';
import { subDays, subMonths, addDays } from 'date-fns';

// Mock merchants by category
const MERCHANTS = {
  food: ['<PERSON>wiggy', 'Zomato', 'McDonald\'s', 'Starbucks', 'Big Bazaar', 'DMart'],
  travel: ['MakeMyTrip', 'Uber', 'Ola', 'IRCTC', 'Goibibo', 'Cleartrip'],
  ecommerce: ['Amazon', 'Flipkart', 'Myntra', 'Nykaa', 'Paytm Mall', 'Ajio'],
  fuel: ['Indian Oil', 'HP Petrol', 'Bharat Petroleum', 'Shell', 'Reliance Petrol'],
  entertainment: ['BookMyShow', 'Netflix', 'Amazon Prime', 'Hotstar', 'Spotify'],
  utilities: ['BSES', 'Airtel', 'Jio', 'Vodafone', 'TATA Power', 'Paytm Bills']
};

export function generateMockTransactions(count: number = 150): Transaction[] {
  const transactions: Transaction[] = [];
  const now = new Date();
  
  for (let i = 0; i < count; i++) {
    const daysAgo = Math.floor(Math.random() * 90); // Last 3 months
    const date = subDays(now, daysAgo);
    
    // Weighted category selection (more realistic spending patterns)
    const categoryWeights = {
      food: 0.35,
      ecommerce: 0.25,
      fuel: 0.15,
      entertainment: 0.10,
      travel: 0.10,
      utilities: 0.05
    };
    
    const random = Math.random();
    let categoryId = 'food';
    let cumulative = 0;
    
    for (const [cat, weight] of Object.entries(categoryWeights)) {
      cumulative += weight;
      if (random <= cumulative) {
        categoryId = cat;
        break;
      }
    }
    
    const category = CASHBACK_CATEGORIES.find(c => c.id === categoryId)!;
    const merchants = MERCHANTS[categoryId as keyof typeof MERCHANTS];
    const merchant = merchants[Math.floor(Math.random() * merchants.length)];
    
    // Generate realistic amounts based on category
    const amountRanges = {
      food: [200, 2000],
      ecommerce: [500, 5000],
      fuel: [1000, 3000],
      entertainment: [150, 800],
      travel: [2000, 15000],
      utilities: [500, 2500]
    };
    
    const [min, max] = amountRanges[categoryId as keyof typeof amountRanges];
    const amount = Math.floor(Math.random() * (max - min) + min);
    
    // Current category gets higher cashback rate, others get 1%
    const currentCategoryId = 'food'; // Assume user's current category is food
    const cashbackRate = categoryId === currentCategoryId ? category.cashbackRate : 1;
    const cashbackEarned = (amount * cashbackRate) / 100;
    
    transactions.push({
      id: `txn-${i + 1}`,
      amount,
      category,
      merchant,
      date,
      description: `${merchant} - ${category.name}`,
      cashbackEarned
    });
  }
  
  return transactions.sort((a, b) => b.date.getTime() - a.date.getTime());
}

export function generateMockUserPreferences(): UserPreferences {
  return {
    userId: 'user-123',
    currentCategory: CASHBACK_CATEGORIES[0], // Food & Dining
    autoOptimizationEnabled: false,
    notificationsEnabled: true,
    lastCategoryChange: subDays(new Date(), 45),
    optimizationScore: 72,
    consecutiveOptimizedMonths: 2
  };
}

export function generateMockBillingCycles(): BillingCycle[] {
  const cycles: BillingCycle[] = [];
  const now = new Date();
  
  for (let i = 0; i < 6; i++) {
    const startDate = subMonths(now, i + 1);
    const endDate = subDays(addDays(startDate, 30), 1);
    
    cycles.push({
      id: `cycle-${i + 1}`,
      startDate,
      endDate,
      totalSpent: Math.floor(Math.random() * 50000) + 20000,
      totalCashback: Math.floor(Math.random() * 2000) + 500,
      optimizationScore: Math.floor(Math.random() * 40) + 60
    });
  }
  
  return cycles;
}

export function generateMockGamificationData(): GamificationData {
  const achievements: Achievement[] = [
    {
      id: 'first-optimization',
      title: 'Smart Spender',
      description: 'Made your first category optimization',
      icon: '🎯',
      unlockedAt: subDays(new Date(), 30),
      progress: 1,
      maxProgress: 1
    },
    {
      id: 'streak-master',
      title: 'Streak Master',
      description: 'Maintained optimization for 3 consecutive months',
      icon: '🔥',
      unlockedAt: null,
      progress: 2,
      maxProgress: 3
    },
    {
      id: 'cashback-king',
      title: 'Cashback King',
      description: 'Earned ₹10,000 in cashback',
      icon: '👑',
      unlockedAt: null,
      progress: 7500,
      maxProgress: 10000
    }
  ];
  
  return {
    currentScore: 72,
    maxScore: 100,
    streak: 2,
    bonusMultiplier: 1.1,
    achievements,
    nextMilestone: {
      title: 'Optimization Expert',
      description: 'Reach 85% optimization score',
      targetScore: 85,
      reward: '2x cashback for next month'
    }
  };
}

export function generateMockDashboardData(): DashboardData {
  const transactions = generateMockTransactions();
  const user = generateMockUserPreferences();
  const billingCycles = generateMockBillingCycles();
  const gamification = generateMockGamificationData();
  
  return {
    user,
    currentBillingCycle: billingCycles[0],
    spendingAnalysis: [], // Will be calculated by the component
    suggestions: [], // Will be calculated by the component
    recentTransactions: transactions.slice(0, 10),
    gamification,
    notifications: []
  };
}
