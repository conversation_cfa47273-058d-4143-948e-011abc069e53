import { CategorySuggestion, SpendingAnalysis, CashbackCategory, NotificationData } from '../types';
import { SpendAnalysisEngine } from './spendAnalysis';
import { NOTIFICATION_TYPES } from '../constants/categories';

export class SuggestionEngine {
  /**
   * Generate personalized suggestions with triggers
   */
  static generatePersonalizedSuggestions(
    spendingAnalysis: SpendingAnalysis[],
    currentCategory: CashbackCategory,
    totalMonthlySpend: number,
    userHistory: { lastSuggestionDate?: Date; dismissedSuggestions?: string[] }
  ): CategorySuggestion[] {
    const baseSuggestions = SpendAnalysisEngine.generateSuggestions(
      spendingAnalysis,
      currentCategory,
      totalMonthlySpend
    );

    // Filter out recently dismissed suggestions
    const filteredSuggestions = baseSuggestions.filter(suggestion => 
      !userHistory.dismissedSuggestions?.includes(suggestion.suggestedCategory.id)
    );

    // Enhance suggestions with personalization
    return filteredSuggestions.map(suggestion => ({
      ...suggestion,
      reason: this.enhanceReason(suggestion, spendingAnalysis),
      confidence: this.adjustConfidence(suggestion, userHistory)
    }));
  }

  /**
   * Check if suggestions should be triggered
   */
  static shouldTriggerSuggestion(
    spendingAnalysis: SpendingAnalysis[],
    currentCategory: CashbackCategory,
    lastTriggerDate?: Date
  ): boolean {
    // Don't trigger more than once per week
    if (lastTriggerDate) {
      const daysSinceLastTrigger = (Date.now() - lastTriggerDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLastTrigger < 7) return false;
    }

    const currentCategorySpending = spendingAnalysis.find(s => s.category.id === currentCategory.id);
    if (!currentCategorySpending) return false;

    // Trigger if current category is not in top 2 spending categories
    const topCategories = spendingAnalysis.slice(0, 2);
    return !topCategories.some(cat => cat.category.id === currentCategory.id);
  }

  /**
   * Generate notification for suggestions
   */
  static createSuggestionNotification(
    suggestion: CategorySuggestion,
    potentialMonthlySavings: number
  ): NotificationData {
    const emoji = suggestion.suggestedCategory.icon;
    const savings = Math.round(potentialMonthlySavings);
    
    return {
      id: `suggestion-${suggestion.suggestedCategory.id}-${Date.now()}`,
      type: NOTIFICATION_TYPES.SUGGESTION,
      title: `${emoji} You're missing out on cashback!`,
      message: `Switch to ${suggestion.suggestedCategory.name} category and earn ₹${savings} more monthly!`,
      actionText: `Switch to ${suggestion.suggestedCategory.name}`,
      timestamp: new Date(),
      isRead: false
    };
  }

  /**
   * Generate auto-optimization notification
   */
  static createAutoOptimizationNotification(
    oldCategory: CashbackCategory,
    newCategory: CashbackCategory,
    expectedIncrease: number
  ): NotificationData {
    return {
      id: `auto-opt-${newCategory.id}-${Date.now()}`,
      type: NOTIFICATION_TYPES.SUCCESS,
      title: '🎯 Auto-optimization activated!',
      message: `Switched from ${oldCategory.name} to ${newCategory.name}. Expected increase: ₹${Math.round(expectedIncrease)}/month`,
      timestamp: new Date(),
      isRead: false
    };
  }

  /**
   * Generate peer benchmarking message
   */
  static generatePeerBenchmark(
    suggestedCategory: CashbackCategory,
    userSegment: string = 'users like you'
  ): string {
    const percentage = Math.floor(Math.random() * 20) + 70; // 70-90%
    return `${percentage}% of ${userSegment} switched to ${suggestedCategory.name} last month.`;
  }

  /**
   * Calculate suggestion priority score
   */
  static calculatePriorityScore(suggestion: CategorySuggestion): number {
    const impactScore = Math.min(50, suggestion.monthlyImpact * 2);
    const confidenceScore = suggestion.confidence * 0.3;
    const urgencyScore = suggestion.potentialIncrease > 500 ? 20 : 10;
    
    return impactScore + confidenceScore + urgencyScore;
  }

  private static enhanceReason(
    suggestion: CategorySuggestion,
    spendingAnalysis: SpendingAnalysis[]
  ): string {
    const suggestedAnalysis = spendingAnalysis.find(s => 
      s.category.id === suggestion.suggestedCategory.id
    );
    
    if (!suggestedAnalysis) return suggestion.reason;

    const transactionFrequency = suggestedAnalysis.transactionCount > 10 ? 'frequently' : 'regularly';
    
    return `${suggestion.reason} You shop ${transactionFrequency} in this category with ${suggestedAnalysis.transactionCount} transactions.`;
  }

  private static adjustConfidence(
    suggestion: CategorySuggestion,
    userHistory: { lastSuggestionDate?: Date; dismissedSuggestions?: string[] }
  ): number {
    let confidence = suggestion.confidence;
    
    // Reduce confidence if user has dismissed similar suggestions
    if (userHistory.dismissedSuggestions?.length) {
      confidence *= 0.9;
    }
    
    // Increase confidence for high-impact suggestions
    if (suggestion.monthlyImpact > 1000) {
      confidence = Math.min(95, confidence * 1.1);
    }
    
    return Math.round(confidence);
  }
}
