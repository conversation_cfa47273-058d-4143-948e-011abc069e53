import { Transaction, SpendingAnalysis, CashbackCategory, CategorySuggestion } from '../types';
import { CASHBACK_CATEGORIES, SPENDING_THRESHOLDS } from '../constants/categories';
import { subMonths, isAfter } from 'date-fns';

export class SpendAnalysisEngine {
  /**
   * Analyze spending patterns over the last 3 months
   */
  static analyzeSpending(
    transactions: Transaction[],
    currentCategory: CashbackCategory,
    months: number = 3
  ): SpendingAnalysis[] {
    const cutoffDate = subMonths(new Date(), months);
    const recentTransactions = transactions.filter(t => isAfter(t.date, cutoffDate));
    
    const categorySpending = new Map<string, {
      totalSpent: number;
      transactionCount: number;
      actualCashback: number;
    }>();

    // Initialize all categories
    CASHBACK_CATEGORIES.forEach(category => {
      categorySpending.set(category.id, {
        totalSpent: 0,
        transactionCount: 0,
        actualCashback: 0
      });
    });

    // Calculate spending by category
    recentTransactions.forEach(transaction => {
      const categoryData = categorySpending.get(transaction.category.id);
      if (categoryData) {
        categoryData.totalSpent += transaction.amount;
        categoryData.transactionCount += 1;
        categoryData.actualCashback += transaction.cashbackEarned;
      }
    });

    const totalSpent = recentTransactions.reduce((sum, t) => sum + t.amount, 0);

    // Convert to SpendingAnalysis array
    return CASHBACK_CATEGORIES.map(category => {
      const data = categorySpending.get(category.id)!;
      const percentage = totalSpent > 0 ? (data.totalSpent / totalSpent) * 100 : 0;
      const potentialCashback = (data.totalSpent * category.cashbackRate) / 100;
      const missedCashback = category.id === currentCategory.id ? 0 : 
        Math.max(0, potentialCashback - data.actualCashback);

      return {
        category,
        totalSpent: data.totalSpent,
        transactionCount: data.transactionCount,
        percentage,
        potentialCashback,
        actualCashback: data.actualCashback,
        missedCashback
      };
    }).sort((a, b) => b.totalSpent - a.totalSpent);
  }

  /**
   * Generate category suggestions based on spending analysis
   */
  static generateSuggestions(
    spendingAnalysis: SpendingAnalysis[],
    currentCategory: CashbackCategory,
    totalMonthlySpend: number
  ): CategorySuggestion[] {
    // Don't suggest if spending is too low
    if (totalMonthlySpend < SPENDING_THRESHOLDS.LOW_USAGE) {
      return [];
    }

    const suggestions: CategorySuggestion[] = [];
    const currentCategorySpending = spendingAnalysis.find(s => s.category.id === currentCategory.id);
    
    if (!currentCategorySpending) return [];

    // Find categories where user spends more than threshold
    const potentialCategories = spendingAnalysis.filter(analysis => 
      analysis.category.id !== currentCategory.id &&
      analysis.percentage >= SPENDING_THRESHOLDS.SUGGESTION_THRESHOLD * 100 &&
      analysis.totalSpent > currentCategorySpending.totalSpent
    );

    potentialCategories.forEach(analysis => {
      const currentCashback = (currentCategorySpending.totalSpent * currentCategory.cashbackRate) / 100;
      const potentialCashback = (analysis.totalSpent * analysis.category.cashbackRate) / 100;
      const increase = potentialCashback - currentCashback;

      if (increase > 0) {
        const confidence = Math.min(95, 50 + (analysis.percentage - 30) * 1.5);
        
        suggestions.push({
          suggestedCategory: analysis.category,
          currentCategory,
          potentialIncrease: increase,
          confidence,
          reason: this.generateReason(analysis, currentCategorySpending),
          monthlyImpact: increase / 3 // Average monthly impact
        });
      }
    });

    return suggestions.sort((a, b) => b.potentialIncrease - a.potentialIncrease);
  }

  /**
   * Calculate optimization score (0-100)
   */
  static calculateOptimizationScore(
    spendingAnalysis: SpendingAnalysis[],
    currentCategory: CashbackCategory
  ): number {
    const totalSpent = spendingAnalysis.reduce((sum, s) => sum + s.totalSpent, 0);
    if (totalSpent === 0) return 100;

    const currentCategoryAnalysis = spendingAnalysis.find(s => s.category.id === currentCategory.id);
    if (!currentCategoryAnalysis) return 0;

    // Find the optimal category (highest spending)
    const optimalCategory = spendingAnalysis[0];
    
    // Calculate actual vs optimal cashback
    const actualCashback = spendingAnalysis.reduce((sum, s) => sum + s.actualCashback, 0);
    const optimalCashback = (optimalCategory.totalSpent * optimalCategory.category.cashbackRate) / 100;
    
    if (optimalCashback === 0) return 100;
    
    const efficiency = (actualCashback / optimalCashback) * 100;
    return Math.min(100, Math.max(0, efficiency));
  }

  private static generateReason(
    suggested: SpendingAnalysis,
    current: SpendingAnalysis
  ): string {
    const spendingDiff = suggested.totalSpent - current.totalSpent;
    const percentageDiff = suggested.percentage - current.percentage;
    
    return `You spend ${percentageDiff.toFixed(1)}% more on ${suggested.category.name} ` +
           `(₹${spendingDiff.toLocaleString()} more) with ${suggested.category.cashbackRate}% cashback rate.`;
  }

  /**
   * Check if user has equal spending across multiple categories
   */
  static hasEqualSpending(spendingAnalysis: SpendingAnalysis[]): boolean {
    const topCategories = spendingAnalysis.slice(0, 3);
    const maxDifference = Math.max(...topCategories.map(c => c.percentage)) - 
                         Math.min(...topCategories.map(c => c.percentage));
    
    return maxDifference < 10; // Less than 10% difference
  }
}
