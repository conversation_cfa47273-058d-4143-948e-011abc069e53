import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Filter, Calendar, DollarSign } from 'lucide-react';

const TransactionMapSimple = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');

  const transactions = [
    {
      id: 1,
      merchant: 'Starbucks Coffee',
      amount: 4.85,
      date: '2024-03-10',
      category: 'Coffee',
      location: 'Downtown Seattle',
      coordinates: { lat: 47.6062, lng: -122.3321 },
      cashback: 0.24,
      time: '8:30 AM'
    },
    {
      id: 2,
      merchant: 'Whole Foods Market',
      amount: 127.43,
      date: '2024-03-09',
      category: 'Groceries',
      location: 'Capitol Hill',
      coordinates: { lat: 47.6205, lng: -122.3212 },
      cashback: 6.37,
      time: '2:15 PM'
    },
    {
      id: 3,
      merchant: 'Shell Gas Station',
      amount: 58.92,
      date: '2024-03-08',
      category: 'Gas',
      location: 'I-5 Corridor',
      coordinates: { lat: 47.5952, lng: -122.3316 },
      cashback: 2.95,
      time: '6:45 PM'
    },
    {
      id: 4,
      merchant: 'Target',
      amount: 89.67,
      date: '2024-03-07',
      category: 'Retail',
      location: 'Northgate',
      coordinates: { lat: 47.7031, lng: -122.3253 },
      cashback: 1.79,
      time: '11:20 AM'
    },
    {
      id: 5,
      merchant: 'Uber Eats',
      amount: 23.45,
      date: '2024-03-06',
      category: 'Food Delivery',
      location: 'Home Delivery',
      coordinates: { lat: 47.6131, lng: -122.3289 },
      cashback: 0.47,
      time: '7:30 PM'
    },
    {
      id: 6,
      merchant: 'Amazon Fresh',
      amount: 156.78,
      date: '2024-03-05',
      category: 'Online Shopping',
      location: 'Bellevue',
      coordinates: { lat: 47.6101, lng: -122.2015 },
      cashback: 3.14,
      time: '10:00 AM'
    }
  ];

  const categories = ['all', 'Coffee', 'Groceries', 'Gas', 'Retail', 'Food Delivery', 'Online Shopping'];

  const filteredTransactions = selectedFilter === 'all' 
    ? transactions 
    : transactions.filter(t => t.category === selectedFilter);

  const getCategoryColor = (category: string) => {
    const colors = {
      'Coffee': 'bg-amber-100 text-amber-800',
      'Groceries': 'bg-emerald-100 text-emerald-800',
      'Gas': 'bg-blue-100 text-blue-800',
      'Retail': 'bg-purple-100 text-purple-800',
      'Food Delivery': 'bg-orange-100 text-orange-800',
      'Online Shopping': 'bg-pink-100 text-pink-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryEmoji = (category: string) => {
    const emojis = {
      'Coffee': '☕',
      'Groceries': '🛒',
      'Gas': '⛽',
      'Retail': '🏪',
      'Food Delivery': '🍕',
      'Online Shopping': '📦'
    };
    return emojis[category as keyof typeof emojis] || '📍';
  };

  return (
    <Card className="w-full bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200">
      <CardHeader className="bg-gradient-to-r from-teal-600 to-cyan-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-6 w-6" />
          Transaction Locations
        </CardTitle>
        <CardDescription className="text-teal-100">
          View your recent transactions and locations
        </CardDescription>
      </CardHeader>
      
      <CardContent className="p-6 space-y-6">
        {/* Filters */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedFilter(category)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedFilter === category
                  ? 'bg-teal-600 text-white'
                  : 'bg-white text-teal-700 hover:bg-teal-50 border border-teal-200'
              }`}
            >
              {category === 'all' ? 'All' : `${getCategoryEmoji(category)} ${category}`}
            </button>
          ))}
        </div>

        {/* Map Placeholder with Enhanced Visualization */}
        <div className="bg-gradient-to-br from-blue-100 via-teal-50 to-green-100 rounded-xl p-8 border-2 border-dashed border-teal-300 shadow-inner">
          <div className="text-center space-y-6">
            <div className="bg-white/80 p-4 rounded-full w-fit mx-auto">
              <MapPin className="h-16 w-16 text-teal-600" />
            </div>
            <div>
              <h3 className="font-bold text-2xl text-teal-800 mb-2">Interactive Transaction Map</h3>
              <p className="text-teal-700 text-lg">
                🗺️ Real-world map with React Leaflet integration
              </p>
              <p className="text-teal-600 text-sm mt-2">
                Shows {filteredTransactions.length} transactions in Seattle area
              </p>
            </div>
            
            {/* Visual Transaction Markers */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
              {filteredTransactions.slice(0, 6).map((transaction, index) => (
                <div key={transaction.id} className="relative flex flex-col items-center">
                  <div 
                    className="w-8 h-8 rounded-full animate-pulse shadow-lg flex items-center justify-center text-white font-bold"
                    style={{
                      backgroundColor: getCategoryColor(transaction.category).includes('amber') ? '#f59e0b' :
                                     getCategoryColor(transaction.category).includes('emerald') ? '#10b981' :
                                     getCategoryColor(transaction.category).includes('blue') ? '#3b82f6' :
                                     getCategoryColor(transaction.category).includes('purple') ? '#8b5cf6' :
                                     getCategoryColor(transaction.category).includes('orange') ? '#f97316' :
                                     getCategoryColor(transaction.category).includes('pink') ? '#ec4899' : '#6b7280',
                      animationDelay: `${index * 300}ms`
                    }}
                  >
                    {getCategoryEmoji(transaction.category)}
                  </div>
                  <div className="mt-2 text-xs bg-white/90 px-3 py-2 rounded-lg shadow-sm border border-gray-200 text-center">
                    <div className="font-bold text-gray-800">{transaction.merchant}</div>
                    <div className="text-gray-600">${transaction.amount}</div>
                    <div className="text-gray-500">{transaction.location}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Transaction List */}
        <div className="space-y-4">
          <h4 className="font-bold text-lg text-teal-800 flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Transactions ({filteredTransactions.length})
          </h4>
          
          <div className="grid gap-3">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{getCategoryEmoji(transaction.category)}</div>
                    <div>
                      <h5 className="font-semibold text-gray-900">{transaction.merchant}</h5>
                      <p className="text-sm text-gray-600">{transaction.location}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getCategoryColor(transaction.category)}>
                          {transaction.category}
                        </Badge>
                        <span className="text-xs text-gray-500">{transaction.date} at {transaction.time}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-bold text-lg text-gray-900">${transaction.amount}</div>
                    <div className="text-sm text-green-600 flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      ${transaction.cashback} cashback
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
          <h4 className="font-semibold text-blue-800 mb-3">📊 Transaction Summary</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-900">
                ${filteredTransactions.reduce((sum, t) => sum + t.amount, 0).toFixed(2)}
              </div>
              <div className="text-sm text-blue-700">Total Spent</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-900">
                ${filteredTransactions.reduce((sum, t) => sum + t.cashback, 0).toFixed(2)}
              </div>
              <div className="text-sm text-green-700">Total Cashback</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-900">
                {filteredTransactions.length}
              </div>
              <div className="text-sm text-purple-700">Transactions</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionMapSimple;
