import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Target, 
  Settings, 
  Bell, 
  Award,
  ArrowRight,
  RefreshCw,
  Info,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Zap
} from 'lucide-react';

// Types
interface CashbackCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  cashbackRate: number;
  description: string;
}

interface SpendingAnalysis {
  category: CashbackCategory;
  totalSpent: number;
  transactionCount: number;
  percentage: number;
  potentialCashback: number;
  actualCashback: number;
  missedCashback: number;
}

interface CategorySuggestion {
  suggestedCategory: CashbackCategory;
  currentCategory: CashbackCategory;
  potentialIncrease: number;
  confidence: number;
  reason: string;
  monthlyImpact: number;
}

// Constants
const CASHBACK_CATEGORIES: CashbackCategory[] = [
  {
    id: 'food',
    name: 'Food & Dining',
    icon: '🍽️',
    color: '#ef4444',
    cashbackRate: 5,
    description: 'Restaurants, food delivery, groceries'
  },
  {
    id: 'travel',
    name: 'Travel',
    icon: '✈️',
    color: '#3b82f6',
    cashbackRate: 4,
    description: 'Flights, hotels, car rentals'
  },
  {
    id: 'ecommerce',
    name: 'E-Commerce',
    icon: '🛒',
    color: '#8b5cf6',
    cashbackRate: 3,
    description: 'Online shopping, digital purchases'
  },
  {
    id: 'fuel',
    name: 'Fuel & Gas',
    icon: '⛽',
    color: '#f59e0b',
    cashbackRate: 2,
    description: 'Gas stations, fuel purchases'
  },
  {
    id: 'entertainment',
    name: 'Entertainment',
    icon: '🎬',
    color: '#10b981',
    cashbackRate: 3,
    description: 'Movies, streaming, gaming'
  },
  {
    id: 'utilities',
    name: 'Utilities',
    icon: '💡',
    color: '#6b7280',
    cashbackRate: 1,
    description: 'Electricity, water, internet bills'
  }
];

const SmartCashbackOptimizer: React.FC = () => {
  const [currentCategory, setCurrentCategory] = useState<CashbackCategory>(CASHBACK_CATEGORIES[0]);
  const [autoOptimizationEnabled, setAutoOptimizationEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Mock spending analysis data
  const spendingAnalysis: SpendingAnalysis[] = useMemo(() => [
    {
      category: CASHBACK_CATEGORIES[0], // Food
      totalSpent: 45000,
      transactionCount: 85,
      percentage: 45,
      potentialCashback: 2250,
      actualCashback: 2250,
      missedCashback: 0
    },
    {
      category: CASHBACK_CATEGORIES[2], // E-commerce
      totalSpent: 28000,
      transactionCount: 32,
      percentage: 28,
      potentialCashback: 840,
      actualCashback: 280,
      missedCashback: 560
    },
    {
      category: CASHBACK_CATEGORIES[1], // Travel
      totalSpent: 15000,
      transactionCount: 8,
      percentage: 15,
      potentialCashback: 600,
      actualCashback: 150,
      missedCashback: 450
    },
    {
      category: CASHBACK_CATEGORIES[3], // Fuel
      totalSpent: 8000,
      transactionCount: 24,
      percentage: 8,
      potentialCashback: 160,
      actualCashback: 80,
      missedCashback: 80
    },
    {
      category: CASHBACK_CATEGORIES[4], // Entertainment
      totalSpent: 3000,
      transactionCount: 12,
      percentage: 3,
      potentialCashback: 90,
      actualCashback: 30,
      missedCashback: 60
    },
    {
      category: CASHBACK_CATEGORIES[5], // Utilities
      totalSpent: 1000,
      transactionCount: 6,
      percentage: 1,
      potentialCashback: 10,
      actualCashback: 10,
      missedCashback: 0
    }
  ], []);

  // Generate suggestions
  const suggestions: CategorySuggestion[] = useMemo(() => {
    const ecommerceAnalysis = spendingAnalysis.find(s => s.category.id === 'ecommerce');
    if (ecommerceAnalysis && currentCategory.id !== 'ecommerce') {
      return [{
        suggestedCategory: ecommerceAnalysis.category,
        currentCategory,
        potentialIncrease: ecommerceAnalysis.missedCashback,
        confidence: 85,
        reason: `You spend 28% more on E-Commerce (₹${ecommerceAnalysis.totalSpent.toLocaleString()}) with ${ecommerceAnalysis.category.cashbackRate}% cashback rate.`,
        monthlyImpact: ecommerceAnalysis.missedCashback / 3
      }];
    }
    return [];
  }, [spendingAnalysis, currentCategory]);

  const optimizationScore = useMemo(() => {
    const totalSpent = spendingAnalysis.reduce((sum, s) => sum + s.totalSpent, 0);
    const actualCashback = spendingAnalysis.reduce((sum, s) => sum + s.actualCashback, 0);
    const potentialCashback = spendingAnalysis.reduce((sum, s) => sum + s.potentialCashback, 0);
    
    if (potentialCashback === 0) return 100;
    return Math.round((actualCashback / potentialCashback) * 100);
  }, [spendingAnalysis]);

  const handleCategorySwitch = async (newCategory: CashbackCategory) => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setCurrentCategory(newCategory);
    setIsLoading(false);
  };

  const totalSpent = spendingAnalysis.reduce((sum, s) => sum + s.totalSpent, 0);
  const totalCashback = spendingAnalysis.reduce((sum, s) => sum + s.actualCashback, 0);
  const potentialIncrease = suggestions.length > 0 ? suggestions[0].potentialIncrease : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Target className="text-blue-600" />
                Smart Cashback Optimizer
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Maximize your rewards with intelligent category suggestions
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Spent (3M)</p>
                <p className="text-xl font-bold text-gray-900">
                  ₹{totalSpent.toLocaleString()}
                </p>
              </div>
              <TrendingUp className="w-6 h-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cashback Earned</p>
                <p className="text-xl font-bold text-green-600">
                  ₹{Math.round(totalCashback).toLocaleString()}
                </p>
              </div>
              <Award className="w-6 h-6 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Optimization Score</p>
                <p className="text-xl font-bold text-purple-600">
                  {optimizationScore}%
                </p>
              </div>
              <Target className="w-6 h-6 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Potential Increase</p>
                <p className="text-xl font-bold text-orange-600">
                  ₹{Math.round(potentialIncrease).toLocaleString()}
                </p>
              </div>
              <ArrowRight className="w-6 h-6 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Optimization Banner */}
      {suggestions.length > 0 && (
        <Card className="relative overflow-hidden bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600">
          <CardContent className="p-6">
            <div className="relative z-10 text-white">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="text-3xl">{suggestions[0].suggestedCategory.icon}</div>
                  <div>
                    <h3 className="text-xl font-bold flex items-center gap-2">
                      <Sparkles className="w-5 h-5" />
                      You're missing out on cashback!
                    </h3>
                    <p className="text-white/90 text-sm">
                      Smart optimization suggestion based on your spending
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm">Switch from</span>
                      <ArrowRight className="w-4 h-4" />
                      <span className="text-sm">Switch to</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-center">
                        <div className="text-2xl mb-1">{currentCategory.icon}</div>
                        <div className="text-sm font-medium">{currentCategory.name}</div>
                        <div className="text-xs opacity-80">{currentCategory.cashbackRate}% cashback</div>
                      </div>
                      
                      <ArrowRight className="w-6 h-6" />
                      
                      <div className="text-center">
                        <div className="text-2xl mb-1">{suggestions[0].suggestedCategory.icon}</div>
                        <div className="text-sm font-medium">{suggestions[0].suggestedCategory.name}</div>
                        <div className="text-xs font-medium">{suggestions[0].suggestedCategory.cashbackRate}% cashback</div>
                      </div>
                    </div>
                  </div>

                  <div className="text-sm mb-4">
                    {suggestions[0].reason}
                  </div>
                </div>

                <div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 mb-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Potential Impact
                    </h4>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Monthly increase:</span>
                        <span className="font-bold text-lg">
                          +₹{Math.round(suggestions[0].monthlyImpact).toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total potential:</span>
                        <span className="font-bold text-lg">
                          +₹{Math.round(suggestions[0].potentialIncrease).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      onClick={() => handleCategorySwitch(suggestions[0].suggestedCategory)}
                      disabled={isLoading}
                      className="flex-1 bg-white text-purple-600 hover:bg-white/90"
                    >
                      {isLoading ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4 mr-2" />
                          Switch Now
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Category & Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Category */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-purple-600" />
              Current Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="text-3xl">{currentCategory.icon}</div>
                  <div>
                    <div className="font-semibold text-gray-900">{currentCategory.name}</div>
                    <div className="text-sm text-gray-600">{currentCategory.description}</div>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm font-medium text-blue-600">
                        {currentCategory.cashbackRate}% cashback
                      </span>
                      <Badge variant="secondary">Current</Badge>
                    </div>
                  </div>
                </div>
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>

            {/* Auto-optimization toggle */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Zap className={`w-5 h-5 ${autoOptimizationEnabled ? 'text-yellow-500' : 'text-gray-400'}`} />
                <div>
                  <div className="font-medium text-gray-900">Auto-Optimization</div>
                  <div className="text-sm text-gray-600">Automatically switch to best category</div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoOptimizationEnabled(!autoOptimizationEnabled)}
              >
                {autoOptimizationEnabled ? 'Enabled' : 'Disabled'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Spending Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              Spending Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {spendingAnalysis.slice(0, 4).map((analysis) => (
                <div key={analysis.category.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="text-xl">{analysis.category.icon}</div>
                    <div>
                      <div className="font-medium text-gray-900">{analysis.category.name}</div>
                      <div className="text-sm text-gray-600">
                        ₹{analysis.totalSpent.toLocaleString()} • {analysis.percentage}%
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-green-600">
                      ₹{Math.round(analysis.actualCashback)}
                    </div>
                    {analysis.missedCashback > 0 && (
                      <div className="text-xs text-red-600">
                        -₹{Math.round(analysis.missedCashback)} missed
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Optimization Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5 text-yellow-600" />
            Optimization Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="flex-1">
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">Current Score</span>
                <span className="text-sm font-bold">{optimizationScore}%</span>
              </div>
              <Progress value={optimizationScore} className="h-3" />
              <p className="text-sm text-gray-600 mt-2">
                {optimizationScore >= 85 ? 'Excellent! You\'re maximizing your cashback potential.' : 
                 optimizationScore >= 70 ? 'Good optimization with room for improvement.' : 
                 'Consider switching to a better category for higher rewards.'}
              </p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{optimizationScore}</div>
              <div className="text-sm text-gray-600">out of 100</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SmartCashbackOptimizer;
