
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, TrendingUp, AlertTriangle, DollarSign, CreditCard, Target } from 'lucide-react';

const SmartSuggestions = () => {
  const suggestions = [
    {
      id: 1,
      type: 'cashback',
      priority: 'high',
      title: 'Maximize Grocery Cashback',
      description: 'Switch to your Discover card for groceries this quarter to earn 5% instead of 1%',
      potentialSavings: 48.50,
      icon: DollarSign,
      category: 'Cashback Optimization',
      color: 'green'
    },
    {
      id: 2,
      type: 'spending',
      priority: 'medium',
      title: 'Reduce Credit Utilization',
      description: 'Your utilization is at 28%. Pay down $500 to get below 20% and improve your credit score',
      potentialSavings: null,
      icon: CreditCard,
      category: 'Credit Health',
      color: 'yellow'
    },
    {
      id: 3,
      type: 'reward',
      priority: 'high',
      title: 'Bonus Category Ending Soon',
      description: 'Gas stations give 5% cashback until March 31st. Fill up now to maximize rewards',
      potentialSavings: 25.00,
      icon: TrendingUp,
      category: 'Limited Time',
      color: 'blue'
    },
    {
      id: 4,
      type: 'warning',
      priority: 'high',
      title: 'Approaching Credit Limit',
      description: 'You\'re using 85% of your available credit. Consider making a payment soon',
      potentialSavings: null,
      icon: AlertTriangle,
      category: 'Alert',
      color: 'red'
    },
    {
      id: 5,
      type: 'goal',
      priority: 'low',
      title: 'Cashback Goal Progress',
      description: 'You\'re $127 away from earning $500 cashback this year. Keep it up!',
      potentialSavings: null,
      icon: Target,
      category: 'Goal Tracking',
      color: 'purple'
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-gradient-to-r from-red-500 to-pink-500';
      case 'medium': return 'bg-gradient-to-r from-yellow-500 to-orange-500';
      case 'low': return 'bg-gradient-to-r from-blue-500 to-cyan-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityBgColor = (priority: string, color: string) => {
    const baseColors = {
      high: 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300',
      medium: 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300',
      low: 'bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-300'
    };
    return baseColors[priority as keyof typeof baseColors] || 'bg-gray-50 border-gray-200';
  };

  const getIconBgColor = (color: string) => {
    const colorMap = {
      green: 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700',
      yellow: 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-700',
      blue: 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700',
      red: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-700',
      purple: 'bg-gradient-to-r from-purple-100 to-violet-100 text-purple-700'
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-gray-100 text-gray-600';
  };

  return (
    <Card className="w-full h-fit bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
      <CardHeader className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-6 w-6" />
          Smart Suggestions
        </CardTitle>
        <CardDescription className="text-indigo-100">
          AI-powered recommendations to optimize your spending
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 p-6">
        {suggestions.map((suggestion) => {
          const IconComponent = suggestion.icon;
          return (
            <div 
              key={suggestion.id} 
              className={`p-4 rounded-xl border-2 ${getPriorityBgColor(suggestion.priority, suggestion.color)} shadow-sm hover:shadow-md transition-shadow`}
            >
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-full shadow-sm ${getIconBgColor(suggestion.color)}`}>
                  <IconComponent className="h-5 w-5" />
                </div>
                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <h4 className="font-bold text-gray-800">{suggestion.title}</h4>
                    <Badge className={`text-white text-xs font-bold ${getPriorityColor(suggestion.priority)}`}>
                      {suggestion.priority.toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 leading-relaxed">{suggestion.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs font-medium border-gray-300">
                      {suggestion.category}
                    </Badge>
                    {suggestion.potentialSavings && (
                      <span className="text-sm font-bold text-green-600 bg-green-50 px-2 py-1 rounded-full">
                        💰 Save ${suggestion.potentialSavings}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
        
        <div className="mt-6 p-6 bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-300 rounded-xl shadow-sm">
          <div className="flex items-center gap-3 mb-3">
            <TrendingUp className="h-6 w-6 text-emerald-600" />
            <span className="font-bold text-lg text-emerald-800">This Month's Impact</span>
          </div>
          <p className="text-gray-700 mb-3 leading-relaxed">
            Following our suggestions could save you <span className="font-bold text-2xl text-emerald-700">$73.50</span> this month
          </p>
          <div className="text-sm text-gray-600 bg-white/50 p-3 rounded-lg">
            📈 Total potential annual savings: <span className="font-bold text-emerald-700">$882</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SmartSuggestions;
