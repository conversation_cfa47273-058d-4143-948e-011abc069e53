/* Leaflet Map Styles */
.leaflet-container {
  height: 100%;
  width: 100%;
  border-radius: 0.75rem;
}

/* Custom marker styles */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

/* Popup styles */
.leaflet-popup-content-wrapper {
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.leaflet-popup-tip {
  background: white;
}

/* Control styles */
.leaflet-control-zoom {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a {
  background-color: white;
  color: #374151;
  border: none;
  font-weight: bold;
  transition: all 0.2s ease;
}

.leaflet-control-zoom a:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

/* Attribution styles */
.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

/* Fullscreen styles */
.leaflet-container.leaflet-fullscreen-on {
  z-index: 9999;
}

/* Marker cluster styles (if needed in future) */
.marker-cluster-small {
  background-color: rgba(181, 226, 140, 0.6);
}

.marker-cluster-small div {
  background-color: rgba(110, 204, 57, 0.6);
}

.marker-cluster-medium {
  background-color: rgba(241, 211, 87, 0.6);
}

.marker-cluster-medium div {
  background-color: rgba(240, 194, 12, 0.6);
}

.marker-cluster-large {
  background-color: rgba(253, 156, 115, 0.6);
}

.marker-cluster-large div {
  background-color: rgba(241, 128, 23, 0.6);
}

/* Custom animations */
@keyframes markerBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.marker-bounce {
  animation: markerBounce 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .leaflet-control-zoom {
    margin-right: 10px;
    margin-bottom: 10px;
  }
  
  .leaflet-popup-content-wrapper {
    max-width: 250px;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .leaflet-control-zoom a {
    background-color: #374151;
    color: #f9fafb;
  }
  
  .leaflet-control-zoom a:hover {
    background-color: #4b5563;
  }
  
  .leaflet-control-attribution {
    background-color: rgba(55, 65, 81, 0.8);
    color: #f9fafb;
  }
}
