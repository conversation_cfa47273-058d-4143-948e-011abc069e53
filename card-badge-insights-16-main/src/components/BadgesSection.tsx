
import React from 'react';
import { Trophy, Star, Target, Zap, Coffee, ShoppingBag, Car, Utensils } from 'lucide-react';
import GitHubBadge from './GitHubBadge';

const BadgesSection = () => {
  const badges = [
    {
      id: 1,
      name: 'Coffee Connoisseur',
      description: 'Spent $500+ on coffee shops',
      icon: Coffee,
      earned: true,
      progress: 100,
      category: 'Dining',
      reward: '5% cashback on coffee',
      color: 'amber',
      detailedDescription: 'You\'ve mastered the art of coffee spending! This badge recognizes your dedication to quality caffeine consumption.',
      requirements: ['Spend $500+ at coffee shops', 'Visit at least 5 different coffee shops'],
      tips: ['Look for coffee shops with bonus cashback offers', 'Consider getting a coffee subscription for regular rewards']
    },
    {
      id: 2,
      name: 'Shopping Star',
      description: 'Made 50+ retail purchases',
      icon: ShoppingBag,
      earned: true,
      progress: 100,
      category: 'Retail',
      reward: '3% cashback on retail',
      color: 'pink',
      detailedDescription: 'You\'re a retail champion! This badge celebrates your consistent shopping habits and smart purchasing decisions.',
      requirements: ['Complete 50+ retail transactions', 'Shop at least 10 different stores'],
      tips: ['Time your purchases during sales events', 'Use cashback apps for additional rewards']
    },
    {
      id: 3,
      name: 'Gas Saver',
      description: 'Spent $1000+ on gas stations',
      icon: Car,
      earned: false,
      progress: 78,
      category: 'Transportation',
      reward: '4% cashback on gas',
      color: 'blue',
      detailedDescription: 'Almost there! This badge rewards consistent fuel purchases and smart transportation spending.',
      requirements: ['Spend $1000+ at gas stations', 'Use the same gas station chain for consistency'],
      tips: ['Look for gas stations with membership programs', 'Fill up during off-peak hours for better deals']
    },
    {
      id: 4,
      name: 'Foodie Explorer',
      description: 'Dined at 25+ different restaurants',
      icon: Utensils,
      earned: false,
      progress: 64,
      category: 'Dining',
      reward: '6% cashback on dining',
      color: 'orange',
      detailedDescription: 'You\'re on your way to becoming a dining expert! This badge celebrates culinary diversity and restaurant exploration.',
      requirements: ['Dine at 25+ different restaurants', 'Try at least 5 different cuisine types'],
      tips: ['Explore new neighborhoods for dining', 'Check for restaurant week specials', 'Use dining reward apps']
    },
    {
      id: 5,
      name: 'Spending Streak',
      description: 'Made purchases for 30 consecutive days',
      icon: Zap,
      earned: true,
      progress: 100,
      category: 'Achievement',
      reward: 'Bonus 1000 points',
      color: 'purple',
      detailedDescription: 'Incredible consistency! This badge recognizes your steady spending habits and regular card usage.',
      requirements: ['Make at least one purchase every day for 30 days', 'Maintain consistent spending patterns'],
      tips: ['Set up recurring subscriptions', 'Use your card for small daily purchases like coffee']
    },
    {
      id: 6,
      name: 'Budget Master',
      description: 'Stayed under budget for 3 months',
      icon: Target,
      earned: false,
      progress: 33,
      category: 'Achievement',
      reward: 'Lower interest rate',
      color: 'green',
      detailedDescription: 'Financial discipline at its finest! This badge rewards responsible spending and budget management.',
      requirements: ['Stay under monthly budget for 3 consecutive months', 'Maintain spending tracking habits'],
      tips: ['Set up spending alerts', 'Review your budget weekly', 'Use the 50/30/20 budgeting rule']
    }
  ];

  const earnedBadges = badges.filter(badge => badge.earned);
  const inProgressBadges = badges.filter(badge => !badge.earned);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-300">
        <div className="flex items-center gap-2">
          <Star className="h-6 w-6 text-yellow-600" />
          <span className="font-bold text-yellow-800">Earned Badges: {earnedBadges.length}</span>
        </div>
        <div className="flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          <span className="font-semibold text-blue-800">{badges.length - earnedBadges.length} In Progress</span>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-bold text-sm text-green-700 uppercase tracking-wider flex items-center gap-2">
          <Trophy className="h-4 w-4" />
          Earned Badges
        </h4>
        <div className="flex flex-wrap gap-3">
          {earnedBadges.map((badge) => (
            <GitHubBadge key={badge.id} {...badge} />
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-bold text-sm text-gray-600 uppercase tracking-wider flex items-center gap-2">
          <Target className="h-4 w-4" />
          In Progress
        </h4>
        <div className="flex flex-wrap gap-3">
          {inProgressBadges.map((badge) => (
            <GitHubBadge key={badge.id} {...badge} />
          ))}
        </div>
      </div>

      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-800 mb-2">🎯 Badge System</h4>
        <p className="text-sm text-blue-700">
          Click on any badge to see detailed requirements, progress, and tips for earning it. 
          Badges unlock special rewards and cashback bonuses!
        </p>
      </div>
    </div>
  );
};

export default BadgesSection;
