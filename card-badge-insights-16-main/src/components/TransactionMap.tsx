import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Filter, Calendar, DollarSign, Maximize2, Minimize2 } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>L<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './TransactionMap.css';

// Types
interface Transaction {
  id: number;
  merchant: string;
  amount: number;
  date: string;
  category: string;
  location: string;
  coordinates: { lat: number; lng: number };
  cashback: number;
  time: string;
}

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icons for different categories
const createCustomIcon = (category: string) => {
  const colors = {
    'Coffee': '#f59e0b',
    'Groceries': '#10b981',
    'Gas': '#3b82f6',
    'Retail': '#8b5cf6',
    'Food Delivery': '#f97316',
    'Online Shopping': '#ec4899'
  };

  const color = colors[category as keyof typeof colors] || '#6b7280';

  return L.divIcon({
    className: 'custom-marker',
    html: `
      <div style="
        background-color: ${color};
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: white;
        font-weight: bold;
      ">
        ${getCategoryEmoji(category)}
      </div>
    `,
    iconSize: [30, 30],
    iconAnchor: [15, 15],
    popupAnchor: [0, -15]
  });
};

const getCategoryEmoji = (category: string) => {
  const emojis = {
    'Coffee': '☕',
    'Groceries': '🛒',
    'Gas': '⛽',
    'Retail': '🏪',
    'Food Delivery': '🍕',
    'Online Shopping': '📦'
  };
  return emojis[category as keyof typeof emojis] || '📍';
};

// Component to fit map bounds to markers
const FitBounds: React.FC<{ transactions: Transaction[] }> = ({ transactions }) => {
  const map = useMap();

  useEffect(() => {
    if (transactions.length > 0) {
      const bounds = L.latLngBounds(
        transactions.map(t => [t.coordinates.lat, t.coordinates.lng])
      );
      map.fitBounds(bounds, { padding: [20, 20] });
    }
  }, [map, transactions]);

  return null;
};

const TransactionMap = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  
  const transactions = [
    {
      id: 1,
      merchant: 'Starbucks Coffee',
      amount: 4.85,
      date: '2024-03-10',
      category: 'Coffee',
      location: 'Downtown Seattle',
      coordinates: { lat: 47.6062, lng: -122.3321 },
      cashback: 0.24,
      time: '8:30 AM'
    },
    {
      id: 2,
      merchant: 'Whole Foods Market',
      amount: 127.43,
      date: '2024-03-09',
      category: 'Groceries',
      location: 'Capitol Hill',
      coordinates: { lat: 47.6205, lng: -122.3212 },
      cashback: 6.37,
      time: '2:15 PM'
    },
    {
      id: 3,
      merchant: 'Shell Gas Station',
      amount: 58.92,
      date: '2024-03-08',
      category: 'Gas',
      location: 'I-5 Corridor',
      coordinates: { lat: 47.5952, lng: -122.3316 },
      cashback: 2.95,
      time: '6:45 PM'
    },
    {
      id: 4,
      merchant: 'Target',
      amount: 89.67,
      date: '2024-03-07',
      category: 'Retail',
      location: 'Northgate',
      coordinates: { lat: 47.7031, lng: -122.3253 },
      cashback: 1.79,
      time: '11:20 AM'
    },
    {
      id: 5,
      merchant: 'Uber Eats',
      amount: 23.45,
      date: '2024-03-06',
      category: 'Food Delivery',
      location: 'Home Delivery',
      coordinates: { lat: 47.6131, lng: -122.3289 },
      cashback: 0.47,
      time: '7:30 PM'
    },
    {
      id: 6,
      merchant: 'Amazon Fresh',
      amount: 156.78,
      date: '2024-03-05',
      category: 'Online Shopping',
      location: 'Bellevue',
      coordinates: { lat: 47.6101, lng: -122.2015 },
      cashback: 3.14,
      time: '10:00 AM'
    },
    {
      id: 7,
      merchant: 'Peet\'s Coffee',
      amount: 6.25,
      date: '2024-03-04',
      category: 'Coffee',
      location: 'University District',
      coordinates: { lat: 47.6587, lng: -122.3138 },
      cashback: 0.31,
      time: '9:15 AM'
    },
    {
      id: 8,
      merchant: 'Costco',
      amount: 234.56,
      date: '2024-03-03',
      category: 'Groceries',
      location: 'Issaquah',
      coordinates: { lat: 47.5301, lng: -122.0326 },
      cashback: 11.73,
      time: '1:45 PM'
    },
    {
      id: 9,
      merchant: 'Chevron',
      amount: 72.18,
      date: '2024-03-02',
      category: 'Gas',
      location: 'Redmond',
      coordinates: { lat: 47.6740, lng: -122.1215 },
      cashback: 3.61,
      time: '5:20 PM'
    },
    {
      id: 10,
      merchant: 'Best Buy',
      amount: 299.99,
      date: '2024-03-01',
      category: 'Retail',
      location: 'Southcenter',
      coordinates: { lat: 47.4598, lng: -122.2565 },
      cashback: 6.00,
      time: '3:30 PM'
    }
  ];

  const categories = ['all', 'Coffee', 'Groceries', 'Gas', 'Retail', 'Food Delivery', 'Online Shopping'];
  
  const filteredTransactions = selectedFilter === 'all' 
    ? transactions 
    : transactions.filter(t => t.category === selectedFilter);



  const getCategoryDotColor = (category: string) => {
    const colors = {
      'Coffee': 'bg-amber-500',
      'Groceries': 'bg-emerald-500',
      'Gas': 'bg-blue-500',
      'Retail': 'bg-purple-500',
      'Food Delivery': 'bg-orange-500',
      'Online Shopping': 'bg-pink-500'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-500';
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Coffee': 'bg-amber-100 text-amber-800',
      'Groceries': 'bg-emerald-100 text-emerald-800',
      'Gas': 'bg-blue-100 text-blue-800',
      'Retail': 'bg-purple-100 text-purple-800',
      'Food Delivery': 'bg-orange-100 text-orange-800',
      'Online Shopping': 'bg-pink-100 text-pink-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Card className={`w-full bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200 ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader className="bg-gradient-to-r from-teal-600 to-cyan-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6" />
            Transaction Locations
          </div>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            {isFullscreen ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
          </button>
        </CardTitle>
        <CardDescription className="text-teal-100">
          View your recent transactions on the interactive map
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        {/* Filter Controls */}
        <div className="flex items-center gap-2 flex-wrap p-4 bg-white/70 rounded-lg border border-teal-200">
          <Filter className="h-5 w-5 text-teal-600" />
          <span className="text-sm font-bold text-teal-800">Filter by category:</span>
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedFilter(category)}
              className={`px-4 py-2 rounded-full text-xs font-bold transition-all duration-200 ${
                selectedFilter === category
                  ? 'bg-gradient-to-r from-teal-500 to-cyan-500 text-white shadow-md'
                  : 'bg-white text-teal-600 hover:bg-teal-50 border border-teal-200'
              }`}
            >
              {category === 'all' ? '🌍 All' : category}
            </button>
          ))}
        </div>

        {/* Interactive Map */}
        <div className={`rounded-xl overflow-hidden border-2 border-teal-300 shadow-lg ${isFullscreen ? 'h-[calc(100vh-300px)]' : 'h-96'}`}>
          <MapContainer
            center={[47.6062, -122.3321]} // Seattle center
            zoom={11}
            style={{ height: '100%', width: '100%' }}
            className="z-10"
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />

            <FitBounds transactions={filteredTransactions} />

            {filteredTransactions.map((transaction) => (
              <Marker
                key={transaction.id}
                position={[transaction.coordinates.lat, transaction.coordinates.lng]}
                icon={createCustomIcon(transaction.category)}
                eventHandlers={{
                  click: () => setSelectedTransaction(transaction)
                }}
              >
                <Popup>
                  <div className="p-2 min-w-[200px]">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{getCategoryEmoji(transaction.category)}</span>
                      <h3 className="font-bold text-gray-800">{transaction.merchant}</h3>
                    </div>

                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Amount:</span>
                        <span className="font-semibold text-gray-800">${transaction.amount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Date:</span>
                        <span>{transaction.date}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Time:</span>
                        <span>{transaction.time}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Location:</span>
                        <span>{transaction.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Category:</span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getCategoryColor(transaction.category)}`}>
                          {transaction.category}
                        </span>
                      </div>
                      <div className="flex justify-between pt-2 border-t border-gray-200">
                        <span>Cashback:</span>
                        <span className="font-semibold text-green-600">${transaction.cashback}</span>
                      </div>
                    </div>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>

        {/* Selected Transaction Details */}
        {selectedTransaction && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-bold text-lg text-blue-800 flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Selected Transaction
              </h4>
              <button
                onClick={() => setSelectedTransaction(null)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ✕ Close
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getCategoryEmoji(selectedTransaction.category)}</span>
                  <div>
                    <h5 className="font-bold text-gray-800">{selectedTransaction.merchant}</h5>
                    <p className="text-sm text-gray-600">{selectedTransaction.location}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Amount:</span>
                  <span className="font-bold text-lg">${selectedTransaction.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Cashback:</span>
                  <span className="font-bold text-green-600">${selectedTransaction.cashback}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Date & Time:</span>
                  <span className="text-sm">{selectedTransaction.date} at {selectedTransaction.time}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Transaction List */}
        <div className="space-y-4">
          <h4 className="font-bold text-lg text-teal-800 uppercase tracking-wider flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Transactions ({filteredTransactions.length})
          </h4>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center gap-4 p-4 bg-white/80 rounded-xl border-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <div className={`p-3 rounded-full ${getCategoryDotColor(transaction.category)} shadow-md`}>
                  <MapPin className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-bold text-gray-800 text-lg">{transaction.merchant}</h5>
                    <span className="font-bold text-xl text-gray-900">${transaction.amount}</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-gray-600 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span className="font-medium">{transaction.date}</span>
                    <span>•</span>
                    <span>{transaction.location}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge className={`border-2 font-medium ${getCategoryColor(transaction.category)}`}>
                      {transaction.category}
                    </Badge>
                    <div className="flex items-center gap-1 bg-green-50 px-3 py-1 rounded-full border border-green-200">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-bold text-green-700">${transaction.cashback} cashback</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 pt-6 border-t-2 border-teal-200">
          <div className="text-center p-4 bg-gradient-to-r from-blue-100 to-cyan-100 rounded-xl border border-blue-300">
            <p className="text-3xl font-bold text-blue-700">{filteredTransactions.length}</p>
            <p className="text-sm font-medium text-blue-600">Transactions</p>
          </div>
          <div className="text-center p-4 bg-gradient-to-r from-purple-100 to-violet-100 rounded-xl border border-purple-300">
            <p className="text-3xl font-bold text-purple-700">
              ${filteredTransactions.reduce((sum, t) => sum + t.amount, 0).toFixed(2)}
            </p>
            <p className="text-sm font-medium text-purple-600">Total Spent</p>
          </div>
          <div className="text-center p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl border border-green-300">
            <p className="text-3xl font-bold text-green-700">
              ${filteredTransactions.reduce((sum, t) => sum + t.cashback, 0).toFixed(2)}
            </p>
            <p className="text-sm font-medium text-green-600">Cashback Earned</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionMap;
