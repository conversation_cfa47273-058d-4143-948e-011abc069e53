import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Filter, Calendar, DollarSign } from 'lucide-react';

const TransactionMap = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  
  const transactions = [
    {
      id: 1,
      merchant: 'Starbucks Coffee',
      amount: 4.85,
      date: '2024-03-10',
      category: 'Coffee',
      location: 'Downtown Seattle',
      coordinates: { lat: 47.6062, lng: -122.3321 },
      cashback: 0.24
    },
    {
      id: 2,
      merchant: 'Whole Foods Market',
      amount: 127.43,
      date: '2024-03-09',
      category: 'Groceries',
      location: 'Capitol Hill',
      coordinates: { lat: 47.6205, lng: -122.3212 },
      cashback: 6.37
    },
    {
      id: 3,
      merchant: 'Shell Gas Station',
      amount: 58.92,
      date: '2024-03-08',
      category: 'Gas',
      location: 'I-5 Corridor',
      coordinates: { lat: 47.5952, lng: -122.3316 },
      cashback: 2.95
    },
    {
      id: 4,
      merchant: 'Target',
      amount: 89.67,
      date: '2024-03-07',
      category: 'Retail',
      location: 'Northgate',
      coordinates: { lat: 47.7031, lng: -122.3253 },
      cashback: 1.79
    },
    {
      id: 5,
      merchant: 'Uber Eats',
      amount: 23.45,
      date: '2024-03-06',
      category: 'Food Delivery',
      location: 'Home Delivery',
      coordinates: { lat: 47.6131, lng: -122.3289 },
      cashback: 0.47
    },
    {
      id: 6,
      merchant: 'Amazon',
      amount: 156.78,
      date: '2024-03-05',
      category: 'Online Shopping',
      location: 'Online Purchase',
      coordinates: { lat: 47.6062, lng: -122.3321 },
      cashback: 3.14
    }
  ];

  const categories = ['all', 'Coffee', 'Groceries', 'Gas', 'Retail', 'Food Delivery', 'Online Shopping'];
  
  const filteredTransactions = selectedFilter === 'all' 
    ? transactions 
    : transactions.filter(t => t.category === selectedFilter);

  const getCategoryColor = (category: string) => {
    const colors = {
      'Coffee': 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 border-amber-300',
      'Groceries': 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-300',
      'Gas': 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border-blue-300',
      'Retail': 'bg-gradient-to-r from-purple-100 to-violet-100 text-purple-800 border-purple-300',
      'Food Delivery': 'bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 border-orange-300',
      'Online Shopping': 'bg-gradient-to-r from-pink-100 to-rose-100 text-pink-800 border-pink-300'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-300';
  };

  const getCategoryDotColor = (category: string) => {
    const colors = {
      'Coffee': 'bg-amber-500',
      'Groceries': 'bg-green-500',
      'Gas': 'bg-blue-500',
      'Retail': 'bg-purple-500',
      'Food Delivery': 'bg-orange-500',
      'Online Shopping': 'bg-pink-500'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-500';
  };

  return (
    <Card className="w-full bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200">
      <CardHeader className="bg-gradient-to-r from-teal-600 to-cyan-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-6 w-6" />
          Transaction Locations
        </CardTitle>
        <CardDescription className="text-teal-100">
          View your recent transactions on the map
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        {/* Filter Controls */}
        <div className="flex items-center gap-2 flex-wrap p-4 bg-white/70 rounded-lg border border-teal-200">
          <Filter className="h-5 w-5 text-teal-600" />
          <span className="text-sm font-bold text-teal-800">Filter by category:</span>
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedFilter(category)}
              className={`px-4 py-2 rounded-full text-xs font-bold transition-all duration-200 ${
                selectedFilter === category
                  ? 'bg-gradient-to-r from-teal-500 to-cyan-500 text-white shadow-md'
                  : 'bg-white text-teal-600 hover:bg-teal-50 border border-teal-200'
              }`}
            >
              {category === 'all' ? '🌍 All' : category}
            </button>
          ))}
        </div>

        {/* Map Placeholder */}
        <div className="bg-gradient-to-br from-blue-100 via-teal-50 to-green-100 rounded-xl p-8 border-2 border-dashed border-teal-300 shadow-inner">
          <div className="text-center space-y-6">
            <div className="bg-white/80 p-4 rounded-full w-fit mx-auto">
              <MapPin className="h-16 w-16 text-teal-600" />
            </div>
            <div>
              <h3 className="font-bold text-2xl text-teal-800 mb-2">Interactive Transaction Map</h3>
              <p className="text-teal-700 text-lg">
                🗺️ Map visualization would show transaction locations with colorful markers
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
              {filteredTransactions.slice(0, 6).map((transaction, index) => (
                <div key={transaction.id} className="relative flex flex-col items-center">
                  <div 
                    className={`w-6 h-6 rounded-full animate-pulse shadow-lg ${getCategoryDotColor(transaction.category)}`}
                    style={{
                      animationDelay: `${index * 300}ms`
                    }}
                  ></div>
                  <div className="mt-2 text-xs bg-white/90 px-3 py-2 rounded-lg shadow-sm border border-gray-200 text-center">
                    <div className="font-bold text-gray-800">{transaction.merchant}</div>
                    <div className="text-gray-600">${transaction.amount}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Transaction List */}
        <div className="space-y-4">
          <h4 className="font-bold text-lg text-teal-800 uppercase tracking-wider flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Transactions ({filteredTransactions.length})
          </h4>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center gap-4 p-4 bg-white/80 rounded-xl border-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <div className={`p-3 rounded-full ${getCategoryDotColor(transaction.category)} shadow-md`}>
                  <MapPin className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-bold text-gray-800 text-lg">{transaction.merchant}</h5>
                    <span className="font-bold text-xl text-gray-900">${transaction.amount}</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-gray-600 mb-3">
                    <Calendar className="h-4 w-4" />
                    <span className="font-medium">{transaction.date}</span>
                    <span>•</span>
                    <span>{transaction.location}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge className={`border-2 font-medium ${getCategoryColor(transaction.category)}`}>
                      {transaction.category}
                    </Badge>
                    <div className="flex items-center gap-1 bg-green-50 px-3 py-1 rounded-full border border-green-200">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-bold text-green-700">${transaction.cashback} cashback</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 pt-6 border-t-2 border-teal-200">
          <div className="text-center p-4 bg-gradient-to-r from-blue-100 to-cyan-100 rounded-xl border border-blue-300">
            <p className="text-3xl font-bold text-blue-700">{filteredTransactions.length}</p>
            <p className="text-sm font-medium text-blue-600">Transactions</p>
          </div>
          <div className="text-center p-4 bg-gradient-to-r from-purple-100 to-violet-100 rounded-xl border border-purple-300">
            <p className="text-3xl font-bold text-purple-700">
              ${filteredTransactions.reduce((sum, t) => sum + t.amount, 0).toFixed(2)}
            </p>
            <p className="text-sm font-medium text-purple-600">Total Spent</p>
          </div>
          <div className="text-center p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl border border-green-300">
            <p className="text-3xl font-bold text-green-700">
              ${filteredTransactions.reduce((sum, t) => sum + t.cashback, 0).toFixed(2)}
            </p>
            <p className="text-sm font-medium text-green-600">Cashback Earned</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionMap;
