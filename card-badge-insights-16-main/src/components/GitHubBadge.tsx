
import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { LucideIcon } from 'lucide-react';

interface GitHubBadgeProps {
  id: number;
  name: string;
  description: string;
  icon: LucideIcon;
  earned: boolean;
  progress: number;
  category: string;
  reward: string;
  color: string;
  detailedDescription?: string;
  requirements?: string[];
  tips?: string[];
}

const GitHubBadge: React.FC<GitHubBadgeProps> = ({
  name,
  description,
  icon: IconComponent,
  earned,
  progress,
  category,
  reward,
  color,
  detailedDescription,
  requirements = [],
  tips = []
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const getBadgeStyle = () => {
    if (earned) {
      return `bg-gradient-to-r from-${color}-500 to-${color}-600 text-white hover:from-${color}-600 hover:to-${color}-700`;
    }
    return `bg-gradient-to-r from-gray-400 to-gray-500 text-white hover:from-gray-500 hover:to-gray-600`;
  };

  const getIconColor = () => {
    return earned ? `text-${color}-600` : 'text-gray-400';
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div className="cursor-pointer group">
          <Badge 
            className={`${getBadgeStyle()} px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg group-hover:scale-105`}
          >
            <IconComponent className={`h-4 w-4 mr-2 ${getIconColor()}`} />
            {name}
            {earned && <span className="ml-2">✓</span>}
            {!earned && <span className="ml-2 text-xs">({progress}%)</span>}
          </Badge>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" side="top">
        <Card className="border-0 shadow-lg">
          <CardHeader className={`bg-gradient-to-r from-${color}-500 to-${color}-600 text-white`}>
            <CardTitle className="flex items-center gap-2">
              <IconComponent className="h-5 w-5" />
              {name}
              {earned && <Badge className="bg-white/20 text-white">Earned</Badge>}
            </CardTitle>
            <CardDescription className="text-white/90">
              {category} • {reward}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 space-y-4">
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Description</h4>
              <p className="text-sm text-gray-600">
                {detailedDescription || description}
              </p>
            </div>
            
            {!earned && (
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Progress</h4>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`bg-gradient-to-r from-${color}-400 to-${color}-600 h-2 rounded-full transition-all duration-500`}
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{progress}% completed</p>
              </div>
            )}
            
            {requirements.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Requirements</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {requirements.map((req, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-gray-400 mt-1">•</span>
                      <span>{req}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {tips.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">💡 Tips</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-400 mt-1">•</span>
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};

export default GitHubBadge;
