
import React from 'react';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8 text-center">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-4">
            Credit Card Dashboard
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            🎯 Track your spending, earn amazing rewards, and optimize your cashback with our colorful insights
          </p>
        </header>

        {/* Credit Card Header */}
        <CreditCardHeader />
        
        {/* Payment Summary Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              💳 Payment Overview
            </h2>
            <PaymentSummary />
          </div>
        </section>
        
        {/* Badges Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              🏆 Achievement Badges
            </h2>
            <BadgesSection />
          </div>
        </section>
        
        {/* Smart Suggestions Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              💡 Smart Suggestions
            </h2>
            <SmartSuggestions />
          </div>
        </section>
        
        {/* Transaction Map Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              🗺️ Transaction Map
            </h2>
            <TransactionMap />
          </div>
        </section>
      </div>
    </div>
  );
};

export default Index;
