<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Card Dashboard - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #f1f5f9 0%, #dbeafe 50%, #e0e7ff 100%); }
        .card-gradient { background: linear-gradient(135deg, #1e293b 0%, #7c3aed 50%, #1e40af 100%); }
        .animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: .5; } }
        .badge-earned { 
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        .badge-progress { 
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            opacity: 0.7;
        }
        .glass { 
            background: rgba(255, 255, 255, 0.8); 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8 text-center">
            <h1 class="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-4">
                Credit Card Dashboard
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                🎯 Track your spending, earn amazing rewards, and optimize your cashback with our colorful insights
            </p>
        </header>

        <!-- Credit Card -->
        <div class="mb-8">
            <div class="relative overflow-hidden card-gradient rounded-2xl shadow-2xl">
                <div class="p-8">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-6"></div>
                    <div class="absolute top-4 right-4 opacity-10">
                        <div class="w-32 h-32 rounded-full border-2 border-white/20"></div>
                        <div class="absolute top-8 left-8 w-16 h-16 rounded-full border-2 border-white/30"></div>
                    </div>
                    
                    <div class="relative z-10">
                        <!-- Header Row -->
                        <div class="flex justify-between items-start mb-8">
                            <div>
                                <h3 class="text-white/80 text-sm font-medium mb-1">Premier Bank</h3>
                                <p class="text-white text-lg font-bold">Credit Card</p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="text-white/60">📶</div>
                                <div class="text-white font-bold text-lg">VISA</div>
                            </div>
                        </div>

                        <!-- Chip -->
                        <div class="mb-8">
                            <div class="w-12 h-9 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center shadow-lg">
                                <div class="w-8 h-6 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm"></div>
                            </div>
                        </div>

                        <!-- Card Number -->
                        <div class="mb-6">
                            <p class="text-white text-2xl font-mono tracking-wider font-medium">
                                **** **** **** 8542
                            </p>
                        </div>

                        <!-- Bottom Row -->
                        <div class="flex justify-between items-end">
                            <div>
                                <p class="text-white/60 text-xs uppercase tracking-wide mb-1">Card Holder</p>
                                <p class="text-white text-sm font-medium tracking-wide">JOHN SMITH</p>
                            </div>
                            <div class="text-right">
                                <p class="text-white/60 text-xs uppercase tracking-wide mb-1">Expires</p>
                                <p class="text-white text-sm font-medium">12/28</p>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-white/10 to-transparent rounded-full transform translate-x-12 translate-y-12"></div>
                </div>
            </div>
        </div>

        <!-- Payment Summary Section -->
        <section class="mb-8">
            <div class="glass rounded-2xl p-6 shadow-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    💳 Payment Overview
                </h2>
                
                <div class="bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200 border rounded-lg">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg p-4">
                        <h3 class="flex items-center gap-2 text-lg font-semibold">
                            💳 Payment Summary
                        </h3>
                        <p class="text-blue-100 text-sm">Your current credit card overview</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="space-y-2 p-4 bg-red-50 rounded-lg border border-red-200">
                                <p class="text-sm text-red-600 font-medium">Current Balance</p>
                                <p class="text-2xl font-bold text-red-700">$2,847.50</p>
                            </div>
                            <div class="space-y-2 p-4 bg-green-50 rounded-lg border border-green-200">
                                <p class="text-sm text-green-600 font-medium">Available Credit</p>
                                <p class="text-2xl font-bold text-green-700">$7,152.50</p>
                            </div>
                        </div>

                        <div class="space-y-2 p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-200">
                            <div class="flex justify-between items-center">
                                <p class="text-sm text-orange-700 font-medium">Credit Utilization</p>
                                <p class="text-sm font-bold text-orange-800">28.5%</p>
                            </div>
                            <div class="w-full bg-orange-100 rounded-full h-3 border border-orange-200">
                                <div class="bg-gradient-to-r from-orange-400 to-red-500 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 28.5%"></div>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                            <div class="flex items-center gap-3 p-3 bg-gradient-to-r from-cyan-50 to-blue-50 rounded-lg border border-cyan-200">
                                <div class="h-8 w-8 text-cyan-600 bg-cyan-100 p-1.5 rounded-full flex items-center justify-center">💰</div>
                                <div>
                                    <p class="text-xs text-cyan-600 font-medium">Minimum Payment</p>
                                    <p class="font-bold text-cyan-800">$87.50</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3 p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg border border-violet-200">
                                <div class="h-8 w-8 text-violet-600 bg-violet-100 p-1.5 rounded-full flex items-center justify-center">📅</div>
                                <div>
                                    <p class="text-xs text-violet-600 font-medium">Due Date</p>
                                    <p class="font-bold text-violet-800">March 15, 2024</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center pt-4 border-t border-gray-200 p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-200">
                            <div class="flex items-center gap-2">
                                <span class="text-emerald-600">📈</span>
                                <span class="text-sm text-emerald-700 font-medium">Cashback Earned: $68.42</span>
                            </div>
                            <span class="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                                On Time
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Badges Section -->
        <section class="mb-8">
            <div class="glass rounded-2xl p-6 shadow-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    🏆 Achievement Badges
                </h2>
                
                <div class="space-y-6">
                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-300">
                        <div class="flex items-center gap-2">
                            <span class="text-yellow-600">⭐</span>
                            <span class="font-bold text-yellow-800">Earned Badges: 3</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600">🎯</span>
                            <span class="font-semibold text-blue-800">3 In Progress</span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-bold text-sm text-green-700 uppercase tracking-wider flex items-center gap-2">
                            <span>🏆</span>
                            Earned Badges
                        </h4>
                        <div class="flex flex-wrap gap-3">
                            <div class="badge-earned text-white px-4 py-2 rounded-lg font-medium cursor-pointer hover:scale-105 transition-transform">
                                ☕ Coffee Connoisseur
                            </div>
                            <div class="badge-earned text-white px-4 py-2 rounded-lg font-medium cursor-pointer hover:scale-105 transition-transform">
                                🛍️ Shopping Star
                            </div>
                            <div class="badge-earned text-white px-4 py-2 rounded-lg font-medium cursor-pointer hover:scale-105 transition-transform">
                                ⚡ Spending Streak
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-bold text-sm text-gray-600 uppercase tracking-wider flex items-center gap-2">
                            <span>🎯</span>
                            In Progress
                        </h4>
                        <div class="flex flex-wrap gap-3">
                            <div class="badge-progress text-white px-4 py-2 rounded-lg font-medium cursor-pointer hover:scale-105 transition-transform">
                                🚗 Gas Saver (78%)
                            </div>
                            <div class="badge-progress text-white px-4 py-2 rounded-lg font-medium cursor-pointer hover:scale-105 transition-transform">
                                🍽️ Foodie Explorer (64%)
                            </div>
                            <div class="badge-progress text-white px-4 py-2 rounded-lg font-medium cursor-pointer hover:scale-105 transition-transform">
                                🎯 Budget Master (33%)
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                        <h4 class="font-semibold text-blue-800 mb-2">🎯 Badge System</h4>
                        <p class="text-sm text-blue-700">
                            Click on any badge to see detailed requirements, progress, and tips for earning it. 
                            Badges unlock special rewards and cashback bonuses!
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Smart Suggestions Section -->
        <section class="mb-8">
            <div class="glass rounded-2xl p-6 shadow-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    💡 Smart Suggestions
                </h2>
                
                <div class="space-y-4">
                    <div class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                        <div class="flex items-center gap-3 mb-2">
                            <span class="text-2xl">💰</span>
                            <h3 class="font-semibold text-green-800">Optimize Your Cashback</h3>
                        </div>
                        <p class="text-green-700 text-sm mb-3">
                            You could earn an extra $15.50 this month by using your card for grocery purchases instead of cash.
                        </p>
                        <button class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                            Learn More
                        </button>
                    </div>

                    <div class="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg border border-blue-200">
                        <div class="flex items-center gap-3 mb-2">
                            <span class="text-2xl">📊</span>
                            <h3 class="font-semibold text-blue-800">Spending Pattern Alert</h3>
                        </div>
                        <p class="text-blue-700 text-sm mb-3">
                            Your dining expenses increased by 25% this month. Consider setting a budget alert to stay on track.
                        </p>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                            Set Alert
                        </button>
                    </div>

                    <div class="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                        <div class="flex items-center gap-3 mb-2">
                            <span class="text-2xl">🎁</span>
                            <h3 class="font-semibold text-purple-800">Reward Opportunity</h3>
                        </div>
                        <p class="text-purple-700 text-sm mb-3">
                            You're only $150 away from earning the "Gas Saver" badge and unlocking 4% cashback on fuel purchases!
                        </p>
                        <button class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors">
                            View Progress
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Transaction Map Section -->
        <section class="mb-8">
            <div class="glass rounded-2xl p-6 shadow-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    🗺️ Transaction Map
                </h2>
                
                <div class="bg-gradient-to-br from-slate-100 to-blue-100 rounded-lg p-8 text-center">
                    <div class="text-6xl mb-4">🗺️</div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Interactive Transaction Map</h3>
                    <p class="text-gray-600 mb-4">
                        Visualize your spending locations and discover patterns in your transaction history.
                    </p>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="text-2xl mb-1">🏪</div>
                            <div class="text-sm font-medium text-gray-700">Grocery Stores</div>
                            <div class="text-xs text-gray-500">15 locations</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="text-2xl mb-1">⛽</div>
                            <div class="text-sm font-medium text-gray-700">Gas Stations</div>
                            <div class="text-xs text-gray-500">8 locations</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="text-2xl mb-1">🍽️</div>
                            <div class="text-sm font-medium text-gray-700">Restaurants</div>
                            <div class="text-xs text-gray-500">22 locations</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="text-2xl mb-1">🛍️</div>
                            <div class="text-sm font-medium text-gray-700">Retail Stores</div>
                            <div class="text-xs text-gray-500">12 locations</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="text-center py-6">
            <p class="text-gray-600">
                🚀 <strong>Credit Card Dashboard</strong> - Built with React, TypeScript, and shadcn/ui
            </p>
            <p class="text-sm text-gray-500 mt-2">
                Interactive demo of the card-badge-insights-16-main project
            </p>
        </footer>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate badges on hover
            document.querySelectorAll('.badge-earned, .badge-progress').forEach(badge => {
                badge.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });
                badge.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Add click handlers for buttons
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                    
                    // Show a simple alert for demo purposes
                    const action = this.textContent.trim();
                    alert(`${action} feature would be implemented in the full application!`);
                });
            });

            // Animate credit utilization bar
            const utilizationBar = document.querySelector('.bg-gradient-to-r.from-orange-400');
            if (utilizationBar) {
                utilizationBar.style.width = '0%';
                setTimeout(() => {
                    utilizationBar.style.width = '28.5%';
                }, 500);
            }
        });
    </script>
</body>
</html>
