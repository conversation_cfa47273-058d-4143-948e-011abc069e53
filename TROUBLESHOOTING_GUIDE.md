# Troubleshooting Guide - React Leaflet Integration

## 🔍 Common Issues and Solutions

### Issue 1: Node.js Not Found
**Error**: `npm: command not found` or `node: command not found`

**Solution**:
1. **Install Node.js**:
   - Visit https://nodejs.org/
   - Download the LTS version (recommended)
   - Install following the installer instructions

2. **Verify Installation**:
   ```bash
   node --version
   npm --version
   ```

3. **Restart Terminal** after installation

### Issue 2: Dependencies Not Installed
**Error**: Module not found errors, missing packages

**Solution**:
```bash
cd card-badge-insights-16-main
npm install
```

### Issue 3: React Leaflet Import Errors
**Error**: `Cannot resolve module 'react-leaflet'` or `Cannot resolve module 'leaflet'`

**Solution**:
```bash
# Install React Leaflet dependencies
npm install leaflet react-leaflet @types/leaflet

# Or if using yarn
yarn add leaflet react-leaflet @types/leaflet
```

### Issue 4: Leaflet CSS Not Loading
**Error**: Map appears broken or unstyled

**Solution**:
1. **Check CSS Import** in `TransactionMap.tsx`:
   ```typescript
   import 'leaflet/dist/leaflet.css';
   ```

2. **Add to main CSS** if needed in `src/index.css`:
   ```css
   @import 'leaflet/dist/leaflet.css';
   ```

### Issue 5: Marker Icons Not Displaying
**Error**: Default markers appear as broken images

**Solution**: The component already includes this fix:
```typescript
// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});
```

### Issue 6: TypeScript Errors
**Error**: TypeScript compilation errors

**Solution**:
1. **Check TypeScript Config** in `tsconfig.json`:
   ```json
   {
     "compilerOptions": {
       "types": ["leaflet"]
     }
   }
   ```

2. **Install Type Definitions**:
   ```bash
   npm install --save-dev @types/leaflet
   ```

### Issue 7: Vite Build Errors
**Error**: Build fails with Vite

**Solution**:
1. **Update Vite Config** in `vite.config.ts`:
   ```typescript
   import { defineConfig } from 'vite'
   import react from '@vitejs/plugin-react'
   import path from 'path'

   export default defineConfig({
     plugins: [react()],
     resolve: {
       alias: {
         '@': path.resolve(__dirname, './src'),
       },
     },
     optimizeDeps: {
       include: ['leaflet']
     }
   })
   ```

## 🚀 Step-by-Step Setup

### 1. **Verify Environment**
```bash
# Check Node.js version (should be 16+)
node --version

# Check npm version
npm --version

# Navigate to project
cd card-badge-insights-16-main
```

### 2. **Install Dependencies**
```bash
# Install all dependencies
npm install

# Verify React Leaflet is installed
npm list react-leaflet
npm list leaflet
```

### 3. **Start Development Server**
```bash
npm run dev
```

### 4. **Check Browser Console**
- Open browser developer tools (F12)
- Check Console tab for any JavaScript errors
- Check Network tab for failed resource loads

## 🔧 Alternative Solutions

### Option 1: Use Static Demo
If React setup is problematic, use the static demo:
```bash
# Open the working HTML demo
open interactive-map-demo.html
```

### Option 2: Simplified Map Component
If React Leaflet causes issues, here's a simplified version:

```typescript
// Simplified TransactionMap without Leaflet
import React from 'react';

const TransactionMap = () => {
  return (
    <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-xl p-8">
      <h3 className="text-xl font-bold mb-4">Transaction Locations</h3>
      <div className="bg-blue-100 rounded-lg p-8 text-center">
        <p>🗺️ Interactive map will load here</p>
        <p className="text-sm text-gray-600 mt-2">
          React Leaflet integration in progress
        </p>
      </div>
    </div>
  );
};

export default TransactionMap;
```

### Option 3: CDN Integration
Add Leaflet via CDN in `index.html`:
```html
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
```

## 📋 Debugging Checklist

- [ ] Node.js installed (version 16+)
- [ ] npm working correctly
- [ ] In correct directory (`card-badge-insights-16-main`)
- [ ] Dependencies installed (`npm install` completed)
- [ ] No TypeScript errors
- [ ] Leaflet CSS imported
- [ ] Browser console shows no errors
- [ ] Network requests successful

## 🆘 Quick Fixes

### Fix 1: Clear Cache and Reinstall
```bash
rm -rf node_modules package-lock.json
npm install
```

### Fix 2: Use Yarn Instead
```bash
npm install -g yarn
yarn install
yarn dev
```

### Fix 3: Check Port Availability
```bash
# If port 5173 is busy, specify different port
npm run dev -- --port 3000
```

## 📞 Getting Help

If you're still experiencing issues, please provide:

1. **Error Message**: Exact error text from terminal or browser console
2. **Environment**: Operating system and Node.js version
3. **Steps Taken**: What commands you've tried
4. **Browser Console**: Any JavaScript errors in browser developer tools

## 🎯 Expected Working State

When everything is working correctly, you should see:

1. ✅ `npm run dev` starts without errors
2. ✅ Browser opens to `http://localhost:5173`
3. ✅ Credit Card Dashboard loads completely
4. ✅ Transaction Map section shows interactive map
5. ✅ Map displays Seattle area with transaction markers
6. ✅ Clicking markers shows popup with transaction details
7. ✅ Category filters work correctly
8. ✅ No console errors in browser developer tools

## 🔄 Fallback Options

If React Leaflet continues to cause issues:

1. **Use Static Demo**: `interactive-map-demo.html` shows full functionality
2. **Simplified Component**: Replace with basic placeholder
3. **Alternative Libraries**: Consider using Google Maps or Mapbox
4. **Server-Side Rendering**: Use Next.js for better Leaflet compatibility

---

**Need immediate help?** Open `interactive-map-demo.html` to see the working map functionality while we troubleshoot the React integration!
