<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transaction Map - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #f1f5f9 0%, #dbeafe 50%, #e0e7ff 100%); }
        .glass { 
            background: rgba(255, 255, 255, 0.8); 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        #map { height: 400px; width: 100%; border-radius: 0.75rem; }
        .custom-marker {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8 text-center">
            <h1 class="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-4">
                Interactive Transaction Map
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                🗺️ Real-world map integration with React Leaflet showing your transaction locations
            </p>
        </header>

        <!-- Transaction Map Section -->
        <section class="mb-8">
            <div class="glass rounded-2xl shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-teal-600 to-cyan-600 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="text-2xl">📍</div>
                            <div>
                                <h2 class="text-2xl font-bold">Transaction Locations</h2>
                                <p class="text-teal-100">View your recent transactions on the interactive map</p>
                            </div>
                        </div>
                        <button class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                            <div class="w-5 h-5">🔍</div>
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex flex-wrap gap-2">
                        <button class="px-4 py-2 bg-teal-600 text-white rounded-lg font-medium">All</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">☕ Coffee</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">🛒 Groceries</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">⛽ Gas</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">🏪 Retail</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">🍕 Food Delivery</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">📦 Online Shopping</button>
                    </div>
                </div>

                <!-- Map Container -->
                <div class="p-6">
                    <div id="map" class="border-2 border-teal-300 shadow-lg"></div>
                </div>

                <!-- Transaction Summary -->
                <div class="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-t border-gray-200">
                    <h3 class="font-bold text-lg text-blue-800 mb-4 flex items-center gap-2">
                        📊 Transaction Summary
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                    <span class="text-green-600 font-bold">10</span>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total Transactions</p>
                                    <p class="font-bold text-gray-900">Last 7 Days</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 font-bold">$</span>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total Amount</p>
                                    <p class="font-bold text-gray-900">$1,383.52</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 font-bold">💰</span>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total Cashback</p>
                                    <p class="font-bold text-green-600">$36.61</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Location Insights -->
                <div class="p-6 bg-gray-50">
                    <h3 class="font-bold text-lg text-gray-800 mb-4 flex items-center gap-2">
                        🎯 Location Insights
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <h4 class="font-semibold text-gray-800 mb-3">Most Visited Areas</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Downtown Seattle</span>
                                    <span class="font-medium">4 visits</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Capitol Hill</span>
                                    <span class="font-medium">2 visits</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Bellevue</span>
                                    <span class="font-medium">2 visits</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <h4 class="font-semibold text-gray-800 mb-3">Category Distribution</h4>
                            <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <div class="w-4 h-4 bg-emerald-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Groceries</span>
                                    <span class="ml-auto font-medium">30%</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Retail</span>
                                    <span class="ml-auto font-medium">25%</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="w-4 h-4 bg-amber-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Coffee</span>
                                    <span class="ml-auto font-medium">20%</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Gas</span>
                                    <span class="ml-auto font-medium">15%</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="w-4 h-4 bg-orange-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Food Delivery</span>
                                    <span class="ml-auto font-medium">10%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features List -->
        <section class="mb-8">
            <div class="glass rounded-2xl p-6 shadow-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    ✨ Interactive Map Features
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-200">
                        <div class="text-2xl mb-2">🗺️</div>
                        <h3 class="font-semibold text-blue-800 mb-2">Real World Map</h3>
                        <p class="text-sm text-blue-700">Interactive OpenStreetMap with zoom, pan, and navigation controls</p>
                    </div>

                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                        <div class="text-2xl mb-2">📍</div>
                        <h3 class="font-semibold text-green-800 mb-2">Custom Markers</h3>
                        <p class="text-sm text-green-700">Color-coded markers for different transaction categories with emojis</p>
                    </div>

                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                        <div class="text-2xl mb-2">💬</div>
                        <h3 class="font-semibold text-purple-800 mb-2">Interactive Popups</h3>
                        <p class="text-sm text-purple-700">Click markers to see detailed transaction information</p>
                    </div>

                    <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
                        <div class="text-2xl mb-2">🔍</div>
                        <h3 class="font-semibold text-orange-800 mb-2">Category Filtering</h3>
                        <p class="text-sm text-orange-700">Filter transactions by category to focus on specific spending types</p>
                    </div>

                    <div class="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-lg p-4 border border-yellow-200">
                        <div class="text-2xl mb-2">📱</div>
                        <h3 class="font-semibold text-yellow-800 mb-2">Responsive Design</h3>
                        <p class="text-sm text-yellow-700">Optimized for mobile, tablet, and desktop viewing</p>
                    </div>

                    <div class="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg p-4 border border-indigo-200">
                        <div class="text-2xl mb-2">🎯</div>
                        <h3 class="font-semibold text-indigo-800 mb-2">Auto-Fit Bounds</h3>
                        <p class="text-sm text-indigo-700">Automatically adjusts map view to show all transaction locations</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="text-center py-6">
            <p class="text-gray-600">
                🚀 <strong>Interactive Transaction Map</strong> - Built with React Leaflet and OpenStreetMap
            </p>
            <p class="text-sm text-gray-500 mt-2">
                Real-world map integration for the Credit Card Dashboard
            </p>
        </footer>
    </div>

    <script>
        // Initialize the map
        const map = L.map('map').setView([47.6062, -122.3321], 11);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Transaction data
        const transactions = [
            { lat: 47.6062, lng: -122.3321, merchant: 'Starbucks Coffee', amount: 4.85, category: 'Coffee', emoji: '☕', color: '#f59e0b' },
            { lat: 47.6205, lng: -122.3212, merchant: 'Whole Foods Market', amount: 127.43, category: 'Groceries', emoji: '🛒', color: '#10b981' },
            { lat: 47.5952, lng: -122.3316, merchant: 'Shell Gas Station', amount: 58.92, category: 'Gas', emoji: '⛽', color: '#3b82f6' },
            { lat: 47.7031, lng: -122.3253, merchant: 'Target', amount: 89.67, category: 'Retail', emoji: '🏪', color: '#8b5cf6' },
            { lat: 47.6131, lng: -122.3289, merchant: 'Uber Eats', amount: 23.45, category: 'Food Delivery', emoji: '🍕', color: '#f97316' },
            { lat: 47.6101, lng: -122.2015, merchant: 'Amazon Fresh', amount: 156.78, category: 'Online Shopping', emoji: '📦', color: '#ec4899' },
            { lat: 47.6587, lng: -122.3138, merchant: 'Peet\'s Coffee', amount: 6.25, category: 'Coffee', emoji: '☕', color: '#f59e0b' },
            { lat: 47.5301, lng: -122.0326, merchant: 'Costco', amount: 234.56, category: 'Groceries', emoji: '🛒', color: '#10b981' },
            { lat: 47.6740, lng: -122.1215, merchant: 'Chevron', amount: 72.18, category: 'Gas', emoji: '⛽', color: '#3b82f6' },
            { lat: 47.4598, lng: -122.2565, merchant: 'Best Buy', amount: 299.99, category: 'Retail', emoji: '🏪', color: '#8b5cf6' }
        ];

        // Add markers to the map
        transactions.forEach(transaction => {
            const customIcon = L.divIcon({
                className: 'custom-marker',
                html: `<div style="background-color: ${transaction.color}; width: 30px; height: 30px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; font-size: 14px;">${transaction.emoji}</div>`,
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            const marker = L.marker([transaction.lat, transaction.lng], { icon: customIcon }).addTo(map);
            
            marker.bindPopup(`
                <div style="padding: 8px; min-width: 200px;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        <span style="font-size: 18px;">${transaction.emoji}</span>
                        <h3 style="margin: 0; font-weight: bold; color: #1f2937;">${transaction.merchant}</h3>
                    </div>
                    <div style="font-size: 14px; color: #6b7280;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span>Amount:</span>
                            <span style="font-weight: 600; color: #1f2937;">$${transaction.amount}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span>Category:</span>
                            <span style="background-color: ${transaction.color}20; color: ${transaction.color}; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">${transaction.category}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding-top: 8px; border-top: 1px solid #e5e7eb;">
                            <span>Cashback:</span>
                            <span style="font-weight: 600; color: #10b981;">$${(transaction.amount * 0.025).toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `);
        });

        // Fit map to show all markers
        const group = new L.featureGroup(map._layers);
        if (Object.keys(group._layers).length > 0) {
            map.fitBounds(group.getBounds().pad(0.1));
        }

        // Add filter functionality
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('button').forEach(b => {
                    b.className = b.className.replace('bg-teal-600 text-white', 'bg-gray-100 text-gray-700');
                });
                
                // Add active class to clicked button
                this.className = this.className.replace('bg-gray-100 text-gray-700', 'bg-teal-600 text-white');
                
                // Simple visual feedback
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
