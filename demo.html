<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Cashback Optimizer - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); }
        .animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: .5; } }
        .animate-bounce-slow { animation: bounce 2s infinite; }
        .gradient-text { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="min-h-screen p-4">
        <div class="max-w-7xl mx-auto space-y-6">
            <!-- Header -->
            <div class="card rounded-2xl shadow-2xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 flex items-center gap-3">
                            🎯 Smart Cashback Optimizer
                        </h1>
                        <p class="text-gray-600 mt-2">
                            Maximize your rewards with intelligent category suggestions
                        </p>
                    </div>
                    <div class="flex items-center gap-4">
                        <div class="relative">
                            <div class="p-3 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors cursor-pointer">
                                🔔
                            </div>
                            <div class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                                3
                            </div>
                        </div>
                        <div class="p-3 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors cursor-pointer">
                            ⚙️
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="card rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Total Spent (3M)</p>
                            <p class="text-2xl font-bold text-gray-900">₹1,24,500</p>
                        </div>
                        <div class="text-2xl">📈</div>
                    </div>
                </div>
                <div class="card rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Cashback Earned</p>
                            <p class="text-2xl font-bold text-green-600">₹3,245</p>
                        </div>
                        <div class="text-2xl">🏆</div>
                    </div>
                </div>
                <div class="card rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Optimization Score</p>
                            <p class="text-2xl font-bold text-purple-600">72%</p>
                        </div>
                        <div class="text-2xl">🎯</div>
                    </div>
                </div>
                <div class="card rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Potential Increase</p>
                            <p class="text-2xl font-bold text-orange-600">₹850</p>
                        </div>
                        <div class="text-2xl">➡️</div>
                    </div>
                </div>
            </div>

            <!-- Optimization Banner -->
            <div class="relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 opacity-90"></div>
                <div class="relative card rounded-2xl shadow-2xl p-6 border border-white border-opacity-20">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="text-3xl animate-bounce-slow">🛒</div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 flex items-center gap-2">
                                    ✨ You're missing out on cashback!
                                </h3>
                                <p class="text-gray-600 text-sm">
                                    Smart optimization suggestion based on your spending
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Switch from</span>
                                    <span class="text-sm text-gray-600">Switch to</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-center">
                                        <div class="text-2xl mb-1">🍽️</div>
                                        <div class="text-sm font-medium text-gray-700">Food & Dining</div>
                                        <div class="text-xs text-gray-500">5% cashback</div>
                                    </div>
                                    <div class="text-purple-600">➡️</div>
                                    <div class="text-center">
                                        <div class="text-2xl mb-1">🛒</div>
                                        <div class="text-sm font-medium text-gray-700">E-Commerce</div>
                                        <div class="text-xs text-green-600 font-medium">3% cashback</div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-4">
                                You spend 45.2% more on E-Commerce (₹18,500 more) with 3% cashback rate.
                            </div>
                        </div>

                        <div>
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 mb-4">
                                <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                    📈 Potential Impact
                                </h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Monthly increase:</span>
                                        <span class="font-bold text-green-600 text-lg">+₹850</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Yearly potential:</span>
                                        <span class="font-bold text-green-600 text-lg">+₹10,200</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200">
                                    ✨ Switch Now
                                </button>
                                <button class="px-4 py-3 border border-gray-300 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Spending Chart -->
                <div class="lg:col-span-2 card rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 flex items-center gap-2">
                                📊 Spending Analysis
                            </h3>
                            <p class="text-gray-600 text-sm mt-1">Last 3 months • Total: ₹1,24,500</p>
                        </div>
                    </div>
                    
                    <!-- Mock Pie Chart -->
                    <div class="h-80 flex items-center justify-center">
                        <div class="relative">
                            <div class="w-64 h-64 rounded-full border-8 border-gray-200 relative overflow-hidden">
                                <div class="absolute inset-0 rounded-full" style="background: conic-gradient(#ef4444 0deg 162deg, #3b82f6 162deg 234deg, #8b5cf6 234deg 288deg, #f59e0b 288deg 324deg, #10b981 324deg 342deg, #6b7280 342deg 360deg);"></div>
                                <div class="absolute inset-8 bg-white rounded-full flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-900">72%</div>
                                        <div class="text-sm text-gray-600">Optimized</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Legend -->
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-6">
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                            <span class="text-sm">🍽️ Food (45%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                            <span class="text-sm">🛒 E-Commerce (20%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                            <span class="text-sm">✈️ Travel (15%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                            <span class="text-sm">⛽ Fuel (10%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                            <span class="text-sm">🎬 Entertainment (6%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-4 h-4 bg-gray-500 rounded-full"></div>
                            <span class="text-sm">💡 Utilities (4%)</span>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Category Switcher -->
                    <div class="card rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 flex items-center gap-2 mb-6">
                            🎯 Category Switcher
                        </h3>
                        
                        <!-- Current Category -->
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="text-3xl">🍽️</div>
                                    <div>
                                        <div class="font-semibold text-gray-900">Food & Dining</div>
                                        <div class="text-sm text-blue-600">5% cashback • Current</div>
                                    </div>
                                </div>
                                <div class="text-green-600">✅</div>
                            </div>
                        </div>

                        <!-- Available Categories -->
                        <div class="space-y-3">
                            <div class="border-2 border-orange-200 bg-orange-50 rounded-xl p-4 cursor-pointer hover:border-orange-300 transition-all">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="text-2xl">🛒</div>
                                        <div>
                                            <div class="font-medium text-gray-900 flex items-center gap-2">
                                                E-Commerce
                                                <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">High Impact</span>
                                            </div>
                                            <div class="text-sm text-orange-600">3% cashback • +₹850/month</div>
                                        </div>
                                    </div>
                                    <div class="text-orange-600">📈 Recommended</div>
                                </div>
                            </div>

                            <div class="border-2 border-gray-200 bg-white rounded-xl p-4 cursor-pointer hover:border-gray-300 transition-all">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="text-2xl">✈️</div>
                                        <div>
                                            <div class="font-medium text-gray-900">Travel</div>
                                            <div class="text-sm text-gray-600">4% cashback</div>
                                        </div>
                                    </div>
                                    <div class="text-gray-400">➡️</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gamification Panel -->
                    <div class="card rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 flex items-center gap-2 mb-6">
                            🏆 Optimization Score
                        </h3>
                        
                        <!-- Score Circle -->
                        <div class="flex justify-center mb-6">
                            <div class="relative w-32 h-32">
                                <div class="w-32 h-32 rounded-full border-8 border-gray-200 relative">
                                    <div class="absolute inset-0 rounded-full border-8 border-transparent border-t-yellow-500 border-r-yellow-500 border-b-yellow-500" style="transform: rotate(260deg);"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="text-3xl font-bold text-yellow-600">72</div>
                                            <div class="text-sm text-gray-600">out of 100</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4">
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="text-orange-600">🔥</span>
                                    <span class="text-sm font-medium text-orange-800">Streak</span>
                                </div>
                                <div class="text-2xl font-bold text-orange-900">2</div>
                                <div class="text-sm text-orange-700">months optimized</div>
                            </div>
                            
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="text-purple-600">⭐</span>
                                    <span class="text-sm font-medium text-purple-800">Bonus</span>
                                </div>
                                <div class="text-2xl font-bold text-purple-900">10%</div>
                                <div class="text-sm text-purple-700">extra cashback</div>
                            </div>
                        </div>

                        <!-- Next Milestone -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <span class="font-medium text-blue-900">🎯 Next Milestone</span>
                                <span class="text-sm text-blue-700">85%</span>
                            </div>
                            <div class="w-full bg-blue-200 rounded-full h-2 mb-2">
                                <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="text-blue-600">🎁</span>
                                <span class="text-sm text-blue-700">Reward: 2x cashback for next month</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="card rounded-xl shadow-lg p-6 text-center">
                <p class="text-gray-600">
                    🚀 <strong>Smart Cashback Optimizer</strong> - Production-ready React component with all PRD features implemented
                </p>
                <p class="text-sm text-gray-500 mt-2">
                    Built with React, TypeScript, Tailwind CSS, Framer Motion, and Recharts
                </p>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate score on load
            const scoreElement = document.querySelector('.text-3xl.font-bold.text-yellow-600');
            if (scoreElement) {
                let currentScore = 0;
                const targetScore = 72;
                const increment = targetScore / 50;
                
                const timer = setInterval(() => {
                    currentScore += increment;
                    if (currentScore >= targetScore) {
                        currentScore = targetScore;
                        clearInterval(timer);
                    }
                    scoreElement.textContent = Math.round(currentScore);
                }, 20);
            }

            // Add click handlers for interactive elements
            document.querySelectorAll('.cursor-pointer').forEach(element => {
                element.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
